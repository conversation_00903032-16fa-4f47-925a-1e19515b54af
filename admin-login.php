<?php
// Start session FIRST - before any output
session_start();

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database and config
include 'config.php';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    if (!empty($email) && !empty($password)) {
        $stmt = $con->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['credits'] = $user['credits'];
            
            $login_success = true;
            $login_message = "Login successful! Welcome " . $user['username'];
        } else {
            $login_error = "Invalid email or password";
        }
    } else {
        $login_error = "Please fill in all fields";
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_unset();
    session_destroy();
    header("Location: admin-login.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h2>🔐 Admin Login</h2>
                    </div>
                    <div class="card-body">
                        
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <!-- User is logged in -->
                            <div class="alert alert-success">
                                <h4>✅ Logged In Successfully!</h4>
                                <p><strong>Username:</strong> <?php echo $_SESSION['username']; ?></p>
                                <p><strong>Role:</strong> <?php echo $_SESSION['role']; ?></p>
                                <p><strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                            </div>
                            
                            <?php if ($_SESSION['role'] === 'Admin'): ?>
                                <div class="alert alert-success">
                                    <h5>🎉 Admin Access Granted!</h5>
                                    <p>You have admin privileges.</p>
                                    <a href="admin/index.php" class="btn btn-success btn-lg">
                                        🚀 Go to Admin Panel
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <h5>⚠️ Not an Admin</h5>
                                    <p>You don't have admin privileges.</p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <a href="?logout=1" class="btn btn-outline-danger">Logout</a>
                                <a href="index.php" class="btn btn-secondary">Main Site</a>
                            </div>
                            
                        <?php else: ?>
                            <!-- Login form -->
                            <?php if (isset($login_error)): ?>
                                <div class="alert alert-danger">
                                    ❌ <?php echo htmlspecialchars($login_error); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (isset($login_success)): ?>
                                <div class="alert alert-success">
                                    ✅ <?php echo htmlspecialchars($login_message); ?>
                                </div>
                                <script>
                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 1000);
                                </script>
                            <?php endif; ?>
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email:</label>
                                    <input type="email" class="form-control" id="email" name="email" required 
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password:</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                
                                <button type="submit" name="login" class="btn btn-primary w-100">
                                    🔐 Login
                                </button>
                            </form>
                            
                            <div class="mt-3 text-center">
                                <small class="text-muted">
                                    Need an admin account? 
                                    <a href="create-admin.php">Create Admin User</a>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                    </div>
                </div>
                
                <!-- System Status -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>System Status</h5>
                    </div>
                    <div class="card-body">
                        <small>
                            <strong>Database:</strong> 
                            <?php 
                            try {
                                $user_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
                                $admin_count = $con->query("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'")->fetch_assoc()['count'];
                                echo "<span class='text-success'>✅ Connected ($user_count users, $admin_count admins)</span>";
                            } catch (Exception $e) {
                                echo "<span class='text-danger'>❌ Error</span>";
                            }
                            ?><br>
                            
                            <strong>Environment:</strong> <?php echo defined('CURRENT_ENVIRONMENT') ? CURRENT_ENVIRONMENT : 'Unknown'; ?><br>
                            <strong>Session:</strong> 
                            <?php echo session_status() === PHP_SESSION_ACTIVE ? '<span class="text-success">✅ Active</span>' : '<span class="text-danger">❌ Inactive</span>'; ?><br>
                            <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                        </small>
                    </div>
                </div>
                
                <!-- Available Admin Users -->
                <?php if (!isset($_SESSION['user_id'])): ?>
                <div class="card mt-3">
                    <div class="card-header">
                        <h6>Available Admin Users</h6>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $admin_users = $con->query("SELECT username, email FROM users WHERE role = 'Admin' ORDER BY created_at DESC");
                            if ($admin_users->num_rows > 0) {
                                echo "<small>";
                                while ($admin = $admin_users->fetch_assoc()) {
                                    echo "<div class='mb-1'>";
                                    echo "<strong>" . htmlspecialchars($admin['username']) . "</strong><br>";
                                    echo "<span class='text-muted'>" . htmlspecialchars($admin['email']) . "</span>";
                                    echo "</div>";
                                }
                                echo "</small>";
                            } else {
                                echo "<small class='text-warning'>No admin users found. <a href='create-admin.php'>Create one</a></small>";
                            }
                        } catch (Exception $e) {
                            echo "<small class='text-danger'>Error loading admin users</small>";
                        }
                        ?>
                    </div>
                </div>
                <?php endif; ?>
                
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
