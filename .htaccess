RewriteEngine On

# EMERGENCY FIX: Immediate redirect for exposed server paths
#ewriteCond %{THE_REQUEST} \s/+home[0-9]+/[^/\s]+/Websites/[^/\s]+/([^\s]*) [NC]
#ewriteRule ^.*$ https://%{HTTP_HOST}/$1 [R=301,L]

# Environment-aware configuration

# Force HTTPS only on production (not localhost)

# URGENT FIX: Server path exposure redirect - MUST BE FIRST
#ewriteCond %{REQUEST_URI} ^/home[0-9]+/[^/]+/Websites/[^/]+/(.*)$ [NC]
#ewriteRule ^.*$ https://%{HTTP_HOST}/$1 [R=301,L]

# Fix for any server internal path exposure - catch all patterns
#ewriteCond %{REQUEST_URI} ^/home[0-9]+/[^/]+/(public_html|Websites)/[^/]+/(.*)$ [NC]
#ewriteRule ^.*$ https://%{HTTP_HOST}/$2 [R=301,L]

# Additional server path patterns
#ewriteCond %{REQUEST_URI} ^/.*/Websites/inspiremental/(.*)$ [NC]
#ewriteRule ^.*$ https://%{HTTP_HOST}/$1 [R=301,L]

# Redirect base URL to home page - PRIORITY RULE
#ewriteRule ^$ home [R=301,L]

# Redirect old index.php URLs to clean URLs
#ewriteCond %{THE_REQUEST} \s/+[^/\s]*\.php\?page=([^\s&]+) [NC]
#ewriteRule ^ %1? [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Test handler routing
RewriteRule ^test-handler/?$ test-handler.php [L,QSA]

# Authentication routing
RewriteRule ^login/?$ login.php [L,QSA]
RewriteRule ^register/?$ register.php [L,QSA]
RewriteRule ^logout/?$ logout.php [L,QSA]

# Main page routing - Universal approach
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^.*/admin/ [NC]
RewriteCond %{REQUEST_URI} !^.*/api/ [NC]
RewriteCond %{REQUEST_URI} !^.*/assets/ [NC]
RewriteCond %{REQUEST_URI} !^.*/uploads/ [NC]
RewriteCond %{REQUEST_URI} !^.*/img/ [NC]
RewriteCond %{REQUEST_URI} !^.*/font/ [NC]
RewriteCond %{REQUEST_URI} !^.*/guide/ [NC]
RewriteCond %{REQUEST_URI} !^.*\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    # Content Security Policy to reduce unwanted requests
    Header always set Content-Security-Policy "default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com https://getbootstrap.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://getbootstrap.com; img-src 'self' data: https:; frame-src 'self' https://www.youtube-nocookie.com https://www.google.com; connect-src 'self' https://api.mymemory.translated.net;"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.md$">
    Order allow,deny
    Deny from all
</Files>

<Files "db-config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*-check.php">
    Order allow,deny
    Deny from all
</Files>

<Files "setup-database.php">
    Order allow,deny
    Deny from all
</Files>
RewriteCond %{HTTP_HOST} ^inspiremental\.org$ [OR]
RewriteCond %{HTTP_HOST} ^www\.inspiremental\.org$
RewriteRule ^/?$ "https\:\/\/inspiremental\.org\/home" [R=301,L]