<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Gemini API configuration
$GEMINI_API_KEY = 'AIzaSyCleXARMP7VWJiAa23BBvWNmtBg9EsrBUA';
$GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' . $GEMINI_API_KEY;

// Get the input data
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['message']) || empty(trim($input['message']))) {
    echo json_encode([
        'success' => false,
        'error' => 'No message provided'
    ]);
    exit;
}

$userMessage = trim($input['message']);

// Create the system prompt for mental health support
$systemPrompt = "You are a compassionate and professional mental health support chatbot for the InspireMental platform. Your role is to:

1. Provide empathetic, supportive responses to users seeking mental health guidance
2. Offer general information about mental health conditions, coping strategies, and wellness tips
3. Encourage users to seek professional help when appropriate
4. Never provide medical diagnoses or replace professional therapy
5. Always maintain a caring, non-judgmental tone
6. Keep responses concise but helpful (2-3 sentences typically)
7. Focus on mental health, emotional wellbeing, stress management, and psychological support

Important guidelines:
- If someone expresses suicidal thoughts or self-harm, immediately encourage them to contact emergency services or a crisis hotline
- Always remind users that you're an AI assistant and not a replacement for professional mental health care
- Be culturally sensitive and respectful
- Provide practical, actionable advice when possible

Please respond to the user's message with empathy and helpful guidance.";

// Prepare the request data for Gemini API
$requestData = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $systemPrompt . "\n\nUser message: " . $userMessage
                ]
            ]
        ]
    ],
    'generationConfig' => [
        'temperature' => 0.7,
        'topK' => 40,
        'topP' => 0.95,
        'maxOutputTokens' => 1024,
    ],
    'safetySettings' => [
        [
            'category' => 'HARM_CATEGORY_HARASSMENT',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ],
        [
            'category' => 'HARM_CATEGORY_HATE_SPEECH',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ],
        [
            'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ],
        [
            'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
        ]
    ]
];

// Initialize cURL
$ch = curl_init();

curl_setopt_array($ch, [
    CURLOPT_URL => $GEMINI_API_URL,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($requestData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
    ],
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => false, // For development only
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

curl_close($ch);

// Handle cURL errors
if ($curlError) {
    echo json_encode([
        'success' => false,
        'error' => 'Connection error: ' . $curlError
    ]);
    exit;
}

// Handle HTTP errors
if ($httpCode !== 200) {
    echo json_encode([
        'success' => false,
        'error' => 'API request failed with status: ' . $httpCode,
        'response' => $response
    ]);
    exit;
}

// Parse the response
$responseData = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid JSON response from API'
    ]);
    exit;
}

// Extract the generated text
if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
    $generatedText = $responseData['candidates'][0]['content']['parts'][0]['text'];
    
    // Clean up the response
    $generatedText = trim($generatedText);
    
    echo json_encode([
        'success' => true,
        'response' => $generatedText
    ]);
} else {
    // Check if the response was blocked by safety filters
    if (isset($responseData['candidates'][0]['finishReason']) && 
        $responseData['candidates'][0]['finishReason'] === 'SAFETY') {
        echo json_encode([
            'success' => true,
            'response' => "I understand you're reaching out for support. For your safety and to ensure you get the best help, I'd recommend speaking with a qualified mental health professional who can provide personalized guidance. If you're in crisis, please contact emergency services or a crisis hotline immediately."
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'No valid response generated',
            'debug' => $responseData
        ]);
    }
}
?>
