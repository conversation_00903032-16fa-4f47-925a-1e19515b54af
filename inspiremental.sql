-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 01, 2025 at 12:04 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `inspiremental`
--

-- --------------------------------------------------------

--
-- Table structure for table `purchases`
--

CREATE TABLE `purchases` (
  `id` int(11) <PERSON>SIGNED NOT NULL,
  `user_id` int(11) UNSIGNED DEFAULT NULL,
  `purchase_amount` int(11) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_status` enum('pending','completed','failed') DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tests`
--

CREATE TABLE `tests` (
  `id` int(11) NOT NULL,
  `test_name` varchar(255) NOT NULL,
  `test_name_kurdish` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `questions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`questions`)),
  `scoring_info` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `tests`
--

INSERT INTO `tests` (`id`, `test_name`, `test_name_kurdish`, `description`, `questions`, `scoring_info`, `created_at`, `updated_at`) VALUES
(1, 'Mental Health Today', 'تەندروستی دەروونیت ئەمڕۆ', 'هەڵسەنگاندنی تەندروستی دەروونی ئەمڕۆ', '[\"ئەمڕۆ هەست بە خۆشی و ئارامی دەکەم.\", \"دەتوانم لەگەڵ کێشەکانم بە باشی مامەڵە بکەم.\", \"هەست بە وزە و چالاکی دەکەم.\", \"دەتوانم بە ئاسانی سەرنج بدەم.\", \"هەست بە ئومێد و ئاکامی باش دەکەم.\", \"دەتوانم بە باشی لەگەڵ کەسانی تر پەیوەندی دروست بکەم.\", \"هەست بە دڵنیایی لە خۆم دەکەم.\", \"دەتوانم بە ئاسانی بڕیار بدەم.\", \"هەست بە ئارامی و هێمنی دەکەم.\", \"بە گشتی ڕازی و خۆشحاڵم.\"]', 'نمرە 0-10: تەندروستی دەروونی باش، 11-25: تەندروستی دەروونی مامناوەند، 26-40: تەندروستی دەروونی خراپ - پێویستە یارمەتی بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(2, 'Depression Test', 'خەمۆکی', 'هەڵسەنگاندن بۆ نیشانەکانی خەمۆکی', '[\"هەست بە خەمۆکی یان بێ هیوایی دەکەم.\", \"حەز لە کارەکانی پێشتر نامێنێت.\", \"کێشەم هەیە لە خەوتن یان زۆر دەخەوم.\", \"هەست بە ماندووبوون یان کەمی وزە دەکەم.\", \"ئارەزووی خواردنم گۆڕاوە (زۆر کەم یان زۆر زیاد).\", \"هەست بە بێ نرخی یان تاوانبارکردنی خۆم دەکەم.\", \"کێشەم هەیە لە سەرنجدان یان بڕیاردان.\", \"زۆر خاو یان بێئارامم.\", \"بیرکردنەوەم لە مردن یان خۆئەزیەتدان هەیە.\", \"هەست دەکەم کەس تێم ناگات یان تەنهام.\"]', 'نمرە 0-10: خەمۆکی کەم، 11-25: خەمۆکی مامناوەند، 26-40: خەمۆکی توند - پێویستە چارەسەری بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(3, 'Anxiety Test', 'دڵەڕاوکێ', 'هەڵسەنگاندن بۆ ئاستی دڵەڕاوکێ', '[\"زۆرجار هەست بە نیگەرانی دەکەم.\", \"سەختە کۆنترۆڵی بیرکردنەوە نیگەرانکەرەکان بکەم.\", \"هەست بە بێئارامی یان لەسەر دەمار بوون دەکەم.\", \"بە ئاسانی ماندوو دەبم.\", \"کێشەم هەیە لە سەرنجدان.\", \"زۆرجار تووڕە یان هەڵچوون.\", \"کێشەم هەیە لە ئارامگرتن.\", \"نیشانە جەستەییەکانم هەیە (دڵتەپین، ئارەقکردن).\", \"دوور دەکەومەوە لە دۆخە دیاریکراوەکان.\", \"نیگەرانی کاریگەری لەسەر ژیانی ڕۆژانەم هەیە.\"]', 'نمرە 0-10: دڵەڕاوکێی کەم، 11-25: دڵەڕاوکێی مامناوەند، 26-40: دڵەڕاوکێی توند - پێویستە چارەسەری بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(4, 'Stress Test', 'فشاری(سترێسی) دەروونی', 'هەڵسەنگاندنی ئاستی فشاری دەروونی', '[\"هەست بە فشاری زۆر لە ژیاندا دەکەم.\", \"سەختە ئارام بم و پشوو بدەم.\", \"زۆرجار سەرم ئێشێت.\", \"کێشەم هەیە لە خەوتن بەهۆی نیگەرانییەوە.\", \"بە ئاسانی تووڕە یان هەڵچوون.\", \"هەست دەکەم زۆر شت لەسەر شانمە.\", \"کێشەم هەیە لە سەرنجدان لە کار.\", \"زۆرجار هەست بە ئاڵۆزی دەکەم.\", \"جەستەم بە شێوەی جیاواز کاردانەوە دەکات (ئێشە، گرژی).\", \"هەست دەکەم ناتوانم لەگەڵ هەموو شتەکان مامەڵە بکەم.\"]', 'نمرە 0-10: فشاری کەم، 11-25: فشاری مامناوەند، 26-40: فشاری توند - پێویستە ڕێگەکانی کەمکردنەوەی فشار فێربیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(5, 'Social Anxiety Test', 'دڵەڕاوکێی کۆمەڵایەتی', 'هەڵسەنگاندن بۆ نیشانەکانی دڵەڕاوکێی کۆمەڵایەتی', '[\"لە دۆخە کۆمەڵایەتییەکاندا هەست بە دڵەڕاوکێ دەکەم.\", \"نیگەران دەبم لەوەی کەسانی تر حوکمم لەسەر بدەن.\", \"دوور دەکەومەوە لە کۆبوونەوە یان بۆنە کۆمەڵایەتییەکان.\", \"لە دۆخە کۆمەڵایەتییەکاندا نیشانە جەستەییەکانم هەیە (سووربوونەوە، ئارەقکردن).\", \"دەترسم لەوەی خۆم شەرمەزار بکەم لەبەردەم کەسانی تر.\", \"کێشەم هەیە لە چاو لە چاو بوون لەگەڵ خەڵک.\", \"دوور دەکەومەوە لە قسەکردن لە گرووپ یان کۆبوونەوەکاندا.\", \"ڕۆژان پێش دۆخە کۆمەڵایەتییەکان نیگەران دەبم.\", \"هەست بە خۆئاگایی دەکەم سەبارەت بە ڕواڵەت یان ڕەفتارەکەم.\", \"ترسە کۆمەڵایەتییەکانم تێکەڵی کار یان پەیوەندییەکانم دەکات.\"]', 'نمرە 0-10: دڵەڕاوکێی کۆمەڵایەتی کەم، 11-25: دڵەڕاوکێی کۆمەڵایەتی مامناوەند، 26-40: دڵەڕاوکێی کۆمەڵایەتی توند - پێویستە چارەسەری بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(6, 'Healthy Lifestyle Test', 'شێوازی ژیانی تەندروست', 'هەڵسەنگاندنی شێوازی ژیانی تەندروست', '[\"بە ڕێکوپێکی وەرزش دەکەم (لانیکەم ٣٠ خولەک لە ڕۆژدا).\", \"خواردنی تەندروست و هاوسەنگ دەخۆم.\", \"بە شێوەیەکی کافی ئاو دەخۆمەوە (لانیکەم ٨ گیلاس لە ڕۆژدا).\", \"خەوی کافیم هەیە (٧-٩ کاتژمێر لە شەودا).\", \"دوور دەکەومەوە لە سگرێت و کحول.\", \"فشاری دەروونی بە شێوەیەکی تەندروست بەڕێوە دەبەم.\", \"بە ڕێکوپێکی پشکنینی پزیشکی دەکەم.\", \"کاتی کافی بۆ ئارامگرتن و پشوودان تەرخان دەکەم.\", \"پەیوەندی باشم لەگەڵ هاوڕێ و خێزان هەیە.\", \"هەوڵ دەدەم کەشوهەوای ئەرێنی بپارێزم.\"]', 'نمرە 0-10: شێوازی ژیانی ناتەندروست، 11-25: شێوازی ژیانی مامناوەند، 26-40: شێوازی ژیانی زۆر تەندروست', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(7, 'Memory Test', 'یادگە', 'هەڵسەنگاندنی توانای یادگە', '[\"بە ئاسانی ناوی کەسان لەبیرم دەچێت.\", \"کێشەم هەیە لە یادکردنەوەی شوێنی دانانی شتەکان.\", \"زۆرجار لەبیرم دەچێت چی دەمویست بکەم.\", \"کێشەم هەیە لە یادکردنەوەی ڕووداوەکانی ڕابردووی نزیک.\", \"لەبیرم دەچێت ژمارە تەلەفۆن یان ناونیشانەکان.\", \"کێشەم هەیە لە یادکردنەوەی وشەکان لە کاتی قسەکردندا.\", \"زۆرجار لەبیرم دەچێت کاتی چاوپێکەوتن یان بۆنەکان.\", \"کێشەم هەیە لە فێربوونی شتی نوێ.\", \"لەبیرم دەچێت ئەو شتانەی کە تازە خوێندمەوە یان بیستوومە.\", \"کێشەم هەیە لە یادکردنەوەی ڕێگاکان بۆ شوێنە ئاشناکان.\"]', 'نمرە 0-10: یادگەی باش، 11-25: یادگەی مامناوەند، 26-40: کێشەی یادگە - پێویستە پشکنینی پزیشکی بکەیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(8, 'Emotional Intelligence Test', 'زیرەکی سۆزداری', 'هەڵسەنگاندنی زیرەکی سۆزداری', '[\"بە باشی هەستەکانی خۆم دەناسم و تێیان دەگەم.\", \"دەتوانم هەستەکانی خۆم بە باشی کۆنترۆڵ بکەم.\", \"بە باشی هەستەکانی کەسانی تر دەناسمەوە.\", \"دەتوانم بە کاریگەری لەگەڵ کەسانی تر پەیوەندی دروست بکەم.\", \"بە باشی گوێ لە کەسانی تر دەگرم.\", \"دەتوانم ملکەچی و لێبوردەکار بم.\", \"بە باشی لەگەڵ گۆڕانکاری مامەڵە دەکەم.\", \"دەتوانم کەسانی تر هان بدەم و پاڵپشتیان بکەم.\", \"بە باشی لە تیمدا کار دەکەم.\", \"دەتوانم ڕەخنە بە شێوەیەکی دروست وەربگرم.\"]', 'نمرە 0-10: زیرەکی سۆزداری کەم، 11-25: زیرەکی سۆزداری مامناوەند، 26-40: زیرەکی سۆزداری بەرز', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(9, 'Type A Personality Test', 'کەسایەتی جۆری A', 'هەڵسەنگاندنی کەسایەتی جۆری A', '[\"زۆرجار پەلە دەکەم و بە خێرایی هەڵسوکەوت دەکەم.\", \"بە سەختی چاوەڕوانی دەکەم و بێ ئارامم کاتێک شتەکان خاو دەبن.\", \"زۆرجار لە یەک کاتدا چەند کار دەکەم.\", \"هەست دەکەم هەمیشە لە پێشبڕکێیەکدام.\", \"بە ئاسانی تووڕە دەبم لە کاتی ترافیک یان ڕیزەکان.\", \"زۆرجار کەسانی تر دەبڕمەوە کاتێک قسە دەکەن.\", \"هەمیشە هەوڵ دەدەم زیاتر لە کەسانی تر بەدەست بهێنم.\", \"کێشەم هەیە لە ئارامگرتن و پشوودان.\", \"زۆرجار دەستم دەکەم بە مشت یان پەنجە لەسەر مێز.\", \"هەست دەکەم کات هەمیشە کەمە.\"]', 'نمرە 0-10: کەسایەتی جۆری B، 11-25: کەسایەتی تێکەڵ، 26-40: کەسایەتی جۆری A - مەترسی نەخۆشی دڵ زیاترە', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(10, 'OCD Test', 'وەسواسی (OCD)', 'هەڵسەنگاندن بۆ نیشانەکانی وەسواسی', '[\"بیرکردنەوە یان وێنەی ناخۆشم هەیە کە ناتوانم ڕێگری لێ بکەم.\", \"هەست دەکەم دەبێت شتەکان بە ڕێکی دیاریکراو ڕێک بخەم.\", \"زۆرجار دەستم دەشۆم یان پاکم دەکەمەوە.\", \"چەندین جار پشکنینی دەرگا یان ئاگرەکان دەکەم.\", \"هەست دەکەم دەبێت کارەکان بە شێوەیەکی دیاریکراو ئەنجام بدەم.\", \"نیگەران دەبم لەوەی زیانی کەسانی تر بگەیەنم.\", \"زۆرجار ژمارەکان یان وشەکان لە مێشکمدا دووبارە دەکەمەوە.\", \"کۆکردنەوە یان هەڵگرتنی شتە بێ پێویستەکان.\", \"زۆر کات بۆ ئەنجامدانی کارە ئاساییەکان دەخایەنم.\", \"ئەم ڕەفتارانە تێکەڵی ژیانی ڕۆژانەم دەکەن.\"]', 'نمرە 0-10: وەسواسی کەم، 11-25: وەسواسی مامناوەند، 26-40: وەسواسی توند - پێویستە چارەسەری بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(11, 'Video Game Addiction Test', 'ئالوودەبوونی یاری ئەلکترۆنی', 'هەڵسەنگاندن بۆ ئالوودەبوونی یاری ئەلکترۆنی', '[\"زۆربەی کاتەکەم بە یاری ئەلکترۆنی بەسەر دەبەم.\", \"نیگەران دەبم یان تووڕە دەبم کاتێک ناتوانم یاری بکەم.\", \"یاری کردن لە کار یان قوتابخانە کاریگەری نەرێنی هەیە.\", \"درۆ دەکەم سەبارەت بە کاتێک کە بۆ یاری تەرخان دەکەم.\", \"یاری وەک ڕێگەیەک بەکاردەهێنم بۆ دوورکەوتنەوە لە کێشەکان.\", \"هەوڵی کەمکردنەوەی یاریم داوە بەڵام سەرکەوتوو نەبووم.\", \"پەیوەندی کۆمەڵایەتی و خێزانیم لەبەر یاری زیانی بینیوە.\", \"خەوی کەم دەکەم بەهۆی یاری کردنەوە.\", \"هەست بە پێویستی زیادکردنی کاتی یاری دەکەم.\", \"بێ یاری هەست بە بێزاری و نائارامی دەکەم.\"]', 'نمرە 0-10: بەکارهێنانی ئاسایی، 11-25: بەکارهێنانی زۆر، 26-40: ئالوودەبوون - پێویستە یارمەتی بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(12, 'PTSD Test', 'تێکچوونی فشاری دەروونی دوای زەبر (PTSD)', 'هەڵسەنگاندن بۆ نیشانەکانی PTSD', '[\"خەونی خراپم هەیە سەبارەت بە ڕووداوە کارەساتبارەکە.\", \"یادەوەری ڕووداوەکە لەناکاو دێتەوە مێشکم.\", \"دوور دەکەومەوە لە شوێن یان کەسانی وەک ڕووداوەکە.\", \"هەست بە بێهەستی یان جیابوونەوە لە کەسانی تر دەکەم.\", \"زۆر ئاگادارم و هەمیشە چاوەڕوانی مەترسی دەکەم.\", \"بە ئاسانی دەتۆقم یان دەپەشۆکێم.\", \"کێشەم هەیە لە خەوتن یان لە خەو مانەوە.\", \"هەست بە تووڕەیی یان هەڕەشە دەکەم.\", \"کێشەم هەیە لە سەرنجدان یان یادکردنەوە.\", \"هەست بە تاوانبارکردنی خۆم دەکەم سەبارەت بە ڕووداوەکە.\"]', 'نمرە 0-10: نیشانەی کەم، 11-25: نیشانەی مامناوەند، 26-40: نیشانەی توند - پێویستە چارەسەری تایبەتمەند بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(13, 'Borderline Personality Disorder Test', 'تێکچوونی کەسایەتی گوماناویی', 'هەڵسەنگاندن بۆ نیشانەکانی تێکچوونی کەسایەتی گوماناویی', '[\"زۆر ترسم لەوەی کەسان بەجێم بهێڵن.\", \"پەیوەندییەکانم ناجێگیرن و پڕ گۆڕانکارین.\", \"هەست بە نادیاری سەبارەت بە خۆم دەکەم.\", \"کارە مەترسیدارەکان دەکەم کاتێک هەستیار دەبم.\", \"هەڵسوکەوتی خۆئەزیەتدان یان هەڕەشەی خۆکوشتنم هەیە.\", \"کەشوهەوای دەروونیم بە خێرایی دەگۆڕێت.\", \"هەست بە بەتاڵی یان پووچەڵی دەکەم.\", \"تووڕەیی توندم هەیە کە سەختە کۆنترۆڵی بکەم.\", \"لە کاتی فشاردا هەست بە پارانۆیا یان جیابوونەوە دەکەم.\", \"هەوڵ دەدەم بە هەر شێوەیەک پەیوەندییەکان ڕابگرم.\"]', 'نمرە 0-10: نیشانەی کەم، 11-25: نیشانەی مامناوەند، 26-40: نیشانەی توند - پێویستە چارەسەری تایبەتمەند بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(14, 'ADHD Test', 'تێکچوونی سەرنج-کەمی/زۆر چالاکیی (ADHD)', 'هەڵسەنگاندن بۆ نیشانەکانی ADHD', '[\"کێشەم هەیە لە سەرنجدان بە وردەکاریەکان.\", \"کێشەم هەیە لە تەواوکردنی ئەرکەکان.\", \"کێشەم هەیە لە ڕێکخستن و پلاندانان.\", \"دوور دەکەومەوە لە ئەرکە سەختەکان.\", \"زۆرجار شتەکانم لەدەست دەچێت.\", \"بە ئاسانی سەرنجم لاودەچێت.\", \"زۆرجار لەبیرم دەچێت شتەکان بکەم.\", \"بێئارامم و ناتوانم بە ئارامی دانیشم.\", \"زۆر قسە دەکەم یان کەسان دەبڕمەوە.\", \"کێشەم هەیە لە چاوەڕوانی کردن.\"]', 'نمرە 0-10: نیشانەی کەم، 11-25: نیشانەی مامناوەند، 26-40: نیشانەی توند - پێویستە هەڵسەنگاندنی پزیشکی بکەیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(15, 'Agoraphobia Test', 'ئاگۆرافۆبیا', 'هەڵسەنگاندن بۆ نیشانەکانی ئاگۆرافۆبیا', '[\"دەترسم لە بوون لە شوێنە قەرەباڵغەکان.\", \"دوور دەکەومەوە لە گواستنەوەی گشتی.\", \"دەترسم لە بوون لە شوێنە داخراوەکان.\", \"دەترسم لە بوون لە ڕیزەکان یان کۆمەڵەکان.\", \"دەترسم لە بوون بە تەنها لە دەرەوەی ماڵەوە.\", \"دەترسم لە بوون لە شوێنە بەرفراوانەکان.\", \"پێویستم بە کەسێک هەیە لەگەڵم بێت کاتێک دەچمە دەرەوە.\", \"دوور دەکەومەوە لە چوونە شوێنە نوێیەکان.\", \"نیشانە جەستەییەکانی ترسم هەیە لە شوێنە دیاریکراوەکان.\", \"ئەم ترسانە کاریگەری لەسەر ژیانی ڕۆژانەم هەیە.\"]', 'نمرە 0-10: ترسی کەم، 11-25: ترسی مامناوەند، 26-40: ئاگۆرافۆبیای توند - پێویستە چارەسەری بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27'),
(16, 'Therapy Need Assessment', 'ئایا پێویستم بە چارەسەرکارە', 'هەڵسەنگاندن بۆ پێویستی چارەسەری', '[\"هەستەکانم کاریگەری لەسەر کار یان قوتابخانەم هەیە.\", \"کێشەم هەیە لە پەیوەندی لەگەڵ کەسانی تر.\", \"بەردەوام هەست بە خەمۆکی یان دڵەڕاوکێ دەکەم.\", \"کێشەم هەیە لە خەوتن یان خواردن.\", \"هەست دەکەم ناتوانم لەگەڵ کێشەکانم مامەڵە بکەم.\", \"بیرکردنەوەم لە خۆئەزیەتدان یان خۆکوشتن هەیە.\", \"ڕەفتارەکانم گۆڕاون و کەسانی تر تێبینیان کردووە.\", \"هەست بە بێ هیوایی یان بێ ئومێدی دەکەم.\", \"بەکارهێنانی مادەی هۆشبەر یان کحولم زیادکردووە.\", \"هەست دەکەم پێویستم بە یارمەتی هەیە بەڵام نازانم چۆن بیدۆزمەوە.\"]', 'نمرە 0-10: پێویستی کەم، 11-25: پێویستی مامناوەند - چارەسەری سوودبەخشە، 26-40: پێویستی زۆر - پێویستە دەستبەجێ چارەسەری بخوازیت', '2025-06-30 07:20:13', '2025-06-30 11:14:27');

-- --------------------------------------------------------

--
-- Table structure for table `test_results`
--

CREATE TABLE `test_results` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `test_id` int(11) NOT NULL,
  `answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`answers`)),
  `total_score` int(11) NOT NULL,
  `completed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `test_results`
--

INSERT INTO `test_results` (`id`, `user_id`, `test_id`, `answers`, `total_score`, `completed_at`) VALUES
(1, 2, 1, '[4,3,3,3,3,1,2,2,3,4]', 28, '2025-06-30 07:22:33'),
(2, 2, 1, '[0,0,0,0,0,0,0,0,0,0]', 0, '2025-06-30 07:25:47'),
(3, 2, 1, '[4,4,4,4,4,4,4,4,4,4]', 40, '2025-06-30 07:26:21'),
(4, 2, 7, '[3,1,2,0,3,1,0,0,1,1]', 12, '2025-06-30 07:29:00'),
(5, 2, 3, '[3,2,2,2,2,2,2,3,2,2]', 22, '2025-06-30 07:48:26'),
(6, 2, 6, '[3,3,3,0,4,2,0,0,2,3]', 20, '2025-06-30 10:25:20'),
(7, 1, 1, '[2,3,0,4,2,0,4,3,4,0]', 22, '2025-06-30 11:17:09');

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) UNSIGNED NOT NULL,
  `user_id` int(11) UNSIGNED DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `transaction_type` enum('purchase','usage') DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `credits` int(11) DEFAULT 0,
  `role` enum('Admin','User','Doctor','Psychologist','Counsellor') DEFAULT 'User',
  `profile_photo` varchar(255) DEFAULT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `token_expiry` datetime DEFAULT NULL,
  `remember_me_token` varchar(255) DEFAULT NULL,
  `remember_me_expiry` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password`, `credits`, `role`, `profile_photo`, `reset_token`, `token_expiry`, `remember_me_token`, `remember_me_expiry`, `created_at`) VALUES
(1, 'mohammed', '<EMAIL>', '$2y$10$qYzSyboYEvaInEHn8RevOukyJToxwH23s8Z/KXq1kAWpaguVTNAY2', 7, 'Admin', 'uploads/profile_photos/1_profile.png', NULL, NULL, 'ed80d7a79bf5673649f68f78596326fd', '2024-11-30 14:05:40', '2024-10-29 13:21:39'),
(2, 'ihsan', '<EMAIL>', '$2y$10$jhvLAZ92ne8oxK/qjcl8VODM.M0UlFcFSm//b37/tvahzrcXMgpLC', 2, 'Counsellor', NULL, NULL, NULL, NULL, NULL, '2024-10-29 13:40:36'),
(3, 'د. ئازاد', '<EMAIL>', '$2y$10$a87YH0pY4JW6e/SDK.pAw./UbXodzg3GPst3bot4AOtfIvvm64A2.', 1, 'Doctor', 'uploads/profile_photos/3_profile.png', NULL, NULL, '36f82a7cc8d22cb463a5b6a9ce67b72b', '2024-12-03 15:37:20', '2024-10-29 14:36:41'),
(4, 'MTAG96', '<EMAIL>', '$2y$10$V4dii/VRiVx2XGbTn.nDaeBugD3j6QZpg/BBOnUtTYN80juN/jW46', 0, 'User', 'uploads/profile_photos/4_profile.jpg', NULL, NULL, 'ad796dda130bafeee18eb80d6f38d049', '2024-11-30 13:09:27', '2024-10-31 12:05:16'),
(5, 'haiv', '<EMAIL>', '$2y$10$C.7hYJWErEUq6h87/BwsP.KGgAOnSIUucVEFF6ErPGBZVeT7goTvu', 0, 'User', NULL, NULL, NULL, 'c31972e71640f66b942a4f47995ab838', '2024-11-30 13:18:28', '2024-10-31 12:18:11');

-- --------------------------------------------------------

--
-- Table structure for table `users_schedule`
--

CREATE TABLE `users_schedule` (
  `schedule_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `day_of_week` enum('Saturday','Sunday','Monday','Tuesday','Wednesday','Thursday','Friday') NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users_schedule`
--

INSERT INTO `users_schedule` (`schedule_id`, `user_id`, `day_of_week`, `start_time`, `end_time`) VALUES
(2, 3, 'Friday', '14:00:00', '15:00:00'),
(6, 3, 'Saturday', '13:00:00', '14:00:00'),
(7, 3, 'Saturday', '17:00:00', '18:00:00'),
(8, 3, 'Wednesday', '13:00:00', '21:00:00'),
(10, 2, 'Sunday', '21:32:00', '22:32:00'),
(11, 2, 'Saturday', '21:00:00', '22:00:00'),
(12, 2, 'Saturday', '22:14:00', '23:30:00'),
(13, 2, 'Monday', '09:00:00', '21:00:00');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `purchases`
--
ALTER TABLE `purchases`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `tests`
--
ALTER TABLE `tests`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `test_results`
--
ALTER TABLE `test_results`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `test_id` (`test_id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users_schedule`
--
ALTER TABLE `users_schedule`
  ADD PRIMARY KEY (`schedule_id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `purchases`
--
ALTER TABLE `purchases`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tests`
--
ALTER TABLE `tests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `test_results`
--
ALTER TABLE `test_results`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `users_schedule`
--
ALTER TABLE `users_schedule`
  MODIFY `schedule_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `test_results`
--
ALTER TABLE `test_results`
  ADD CONSTRAINT `test_results_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `test_results_ibfk_2` FOREIGN KEY (`test_id`) REFERENCES `tests` (`id`);

--
-- Constraints for table `users_schedule`
--
ALTER TABLE `users_schedule`
  ADD CONSTRAINT `users_schedule_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
