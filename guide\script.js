//Home MainList
// Define show and hide tooltip functions
function showTooltip(event) {
  const tooltip = document.querySelector(".talkBox");
  tooltip.style.display = "block";
  const title = event.target.getAttribute("title");
  tooltip.querySelector(".textTalk").innerText = title;
}

function hideTooltip() {
  const tooltip = document.querySelector(".talkBox");
  tooltip.querySelector(".textTalk").innerText = "";
  tooltip.style.display = "none";
}

// Attach event listeners to each area
for (let i = 1; i <= 7; i++) {
  const area = document.querySelector(`#area${i}`);
  area.addEventListener("mouseover", showTooltip);
  area.addEventListener("mouseout", hideTooltip);
}
//END Home MainList

//PreOpen Popup
document.addEventListener("DOMContentLoaded", function() {
    const popup = document.getElementById('popup');
    const emojiContainer = document.getElementById('emoji-container');

    function showPopup() {
        popup.style.display = 'block';
    }

    function hidePopup() {
        popup.style.display = 'none';
    }

    function handleEmojiClick(emoji) {
        const motivations = {
            '😞': [
                'سبەی دەرفەتێکی نوێیە بۆ ڕۆژێکی باشتر.',
                'تەحەددیاتەکان بەهێزترت دەکەن. تۆ بەم شتەدا تێدەپەڕیت.',
                'لەبیرت بێت، کاتە سەختەکان بەردەوام نین، مرۆڤە سەختەکان بەردەوام دەبن.',
                'پشوویەک وەربگرە، بارگاوی بکەرەوە و بەهێزتر بگەڕێوە.',
                'تۆ هێزی ئەوەت هەیە شتەکان بگۆڕیت.',
                'هەندێک جار باشترین شت کە دەتوانیت بیکەیت پشوو بدەیت و بارگاوی بکەیتەوە.',
                'ناخۆشی ئەو خاکەیە کە خۆڕاگری تێیدا گەشە دەکات.',
                'گەشتەکەت ناوازەیە، هەروەها خاڵە بەهێزەکانت.',
                'تۆ بەتەنیا نیت; ئەگەر پێویست بوو دەستت بۆ پشتگیری درێژ بکە.',
                'تەنانەت لە ڕۆژانی سەختیشدا، ساتەکانی جوانی هەیە.',
                'پێشتر بەسەر تەحەددیاتەکاندا زاڵ بوویت، و دەتوانیت دووبارە بیکەیتەوە.',
                'خۆر ئاوا دەبێت بۆ ئەوەی جارێکی تر هەڵبێتەوە. سبەی سەرەتایەکی نوێیە.',
                'ڕەنگە ئێستا هەوڵەکانت دیار نەبن، بەڵام گرنگن.',
                'ئەوە باشە کە باش نەبیت. تۆ مرۆڤیت و شایەنی گرنگیپێدانیت.',
                'لە پلانە گەورەکەی ژیاندا، ئەمڕۆ تەنها بابەتێکە.',
                'توانای تۆ بێ سنوورە. گەشتەکە لە باوەش بگرە.',
                'پاشکەوتن ڕێکخستنێکە بۆ گەڕانەوە. بەردەوام بە لە هەنگاونان بەرەو پێشەوە.',
                'تۆ لەوە بەهێز تریت کە بیری لێ دەکەیتەوە. باوەڕت بە خۆت هەبێ.',
                'هەنگاوێک بە یەک هەنگاو بنێ؛ پێشکەوتن پێشکەوتنە.',
                'سەختییەکان دەرفەتن بۆ گەشەکردن و فێربوون.',
                'خۆڕاگریت بەڵگەیە لەسەر هێزی ناوەوەت.',
                'ئامێز بگرە لە وەرچەرخان و ڕۆیشتنی ژیان. سبەی دەرفەتی نوێ دەهێنێت.',
                'باوەڕ بە سیحری سەرەتای نوێ هەبێت.'
            ],
            '😐': [
                'تەرکیز لەسەر ساتەوەختە ئەرێنییەکان بکە، هەرچەندە بچووک بن.',
                'ژیان گەشتێکە کە هەردوو ڕێگای نەرم و چەقبەستووی هەیە.',
                'سەرکەوتنە بچووکەکان بەرز دەنرخێن. تۆ کارێکی نایاب دەکەیت!',
                'لە ئەمڕۆوە فێربە؛ سبەی چانسێکە بۆ باشتربوون.',
                'تۆ خۆڕاگریت. گەشتی پێشەوە لە باوەش بگرن.',
                'هەندێک جار تەحەددیاتی ژیان دەرفەتی پۆشراون.',
                'هەر ڕۆژێک کانڤاسێکی نوێیە بۆ نیگارکێشانی چیرۆکەکەت.',
                'ساتی ئێستا دیارییەکە؛ بە نرخی بزانە.',
                'ڕەنگە گەشتەکەت چالاک بێت، بەڵام بە شێوەیەکی ناوازە هی تۆیە.',
                'نادڵنیایی لە باوەش بگرن؛ ئەوە بەردێکی هەنگاوە بۆ گەشەکردن.',
                'زۆرجار ڕێگای سەرکەوتن بە تەحەددیات خۆش دەکرێت.',
                'سەبر کلیلە. کاتی تۆ دێت.',
                'پێشکەوتنی بچووک هێشتا پێشکەوتنە. ئاهەنگی بۆ بگێڕن.',
                'ڕیتمی ژیان هەردوو بەرزی و نزم لەخۆدەگرێت.',
                'ساتێک بۆ قەدرزانینی هێزی ناوەوەت تەرخان بکە.',
                'تۆ لەوە خۆڕاگرتریت کە بیری لێدەکەیتەوە. بەردەوامبە.',
                'سەرکەوتن گەشتێکە نەک جێگەی مەبەست.',
                'ژیان پڕە لە سەرسوڕمان. نەزانراوەکان لە باوەش بگرن.',
                'گەشتەکە بەقەد شوێنی مەبەست گرنگە.',
                'چیرۆکەکەت هێشتا دەنووسرێت؛ بیکە بە یەکێکی باش.',
                'ئاهەنگ بگێڕن بۆ پێشکەوتنەکان، هەرچەندە بچووک بن.',
                'هەموو هەنگاوێک بۆ پێشەوە هەنگاوێکە بە ئاراستەیەکی دروستدا.',
                'باشترینەکان بەڕێوەن. بەردەوام بە لە هەنگاونان بەرەو پێشەوە.',
                'باوەڕ بە توانای خۆت بکە بۆ زاڵبوون بەسەر ئاستەنگەکاندا.'
            ],
            '😊': [
                'بەردەوام بە لە بڵاوکردنەوەی ئەو ڤایبە ئەرێنییانە. تۆ سەرسوڕهێنەر دەکەیت!',
                'ئەرێنییەت درمییە. بەردەوام بە لە درەوشانەوە!',
                'ئاهەنگ بگێڕن بۆ سەرکەوتنەکانتان، گەورە و بچووک.',
                'دەستخۆشی بۆ ڕۆژێکی نایاب! هیوادارم بەردەوام بێت لە سەرسوڕهێنەر.',
                'ڕوانگەی ئەرێنی تۆ دەرگا بەڕووی ئەگەرە بێکۆتاکاندا دەکاتەوە.',
                'جیهان بە وزەی ئەرێنی تۆ گەشاوەترە.',
                'جوانی هەر ساتێک لە باوەش بگرە.',
                'زەردەخەنەکەت هێزی ئەوەی هەیە ڕۆژی کەسێک گەشاوە بکات.',
                'هەموو کردەوەیەکی بچووکی میهرەبانی جیاوازییەکی گەورە دروست دەکات.',
                'باوەڕ بەو سیحرەی ناو خۆت بکە. بێ سنوورە.',
                'ئەرێنی تیشکدانەوە؛ جیهان دەکاتە شوێنێکی باشتر.',
                'حەماسەتت سووتەمەنی سەرکەوتنەکانتە.',
                'ڕووناکی خۆت لەگەڵ جیهاندا دابەش بکە؛ پێویستی پێیەتی.',
                'سەرکەوتن بەدوای ئەو کەسانەدا دێت کە باوەڕیان بە خەونەکانیان هەیە.',
                'لە هەر شوێنێک بچیت خۆشی بڵاوبکەرەوە؛ ئەوە دیارییەکە بۆ ئەوانی تر.',
                'بیرکردنەوەی ئەرێنی تۆ هێزێکی بەهێزە بۆ چاکە.',
                'ئاهەنگ بگێڕە بۆ تایبەتمەندی خۆت؛ ڕەنگ بۆ جیهان زیاد دەکات.',
                'میهرەبانیت کاریگەری شەپۆلی هەیە؛ دەست دەخاتە سەر ژیانی زۆر کەس.',
                'هەرچەندە زیاتر ئاهەنگ بگێڕیت بۆ ژیان، زیاتر ئاهەنگگێڕان هەیە.',
                'پۆتانسێلەکەت وەک بڵێسەی ئاگر وایە؛ با بە گەشاوەیی بدرەوشێتەوە.',
                'ژیانێک دروست بکە کە خۆشت دەوێت بە گرنگیدان بە چاکەکان.',
                'هەڵوێستی تۆ ئاراستەت دیاری دەکات. بە ئەرێنی بیهێڵەرەوە.',
                'جیهان پڕە لە ئەگەر؛ بە قۆڵی کراوە لە باوەشیان بگرن.',
                'گەشتەکەت زنجیرەیەک ساتەوەختی جوانە. چێژیان لێ ببینه‌.'
            ]
        };

    const randomMotivation = getRandomElement(motivations[emoji]);
    alert(randomMotivation);
    hidePopup();
}

    function getRandomElement(array) {
        const randomIndex = Math.floor(Math.random() * array.length);
        return array[randomIndex];
    }

    //Show the popup when the page is loaded
    //showPopup();
    //Show the popup every 24 hours
                function setLastShownTime() {
            const now = new Date();
            const twentyFourHoursLater = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            document.cookie = `lastShownTime=${twentyFourHoursLater.toUTCString()}; expires=${twentyFourHoursLater.toUTCString()}`;
        }

        function getLastShownTime() {
            const cookies = document.cookie.split(';');
            for (const cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'lastShownTime') {
                    return new Date(value);
                }
            }
            return null;
        }

        // Check if 24 hours have passed since the last time the popup was shown
        const lastShownTime = getLastShownTime();
        const currentTime = new Date();
        if (!lastShownTime || currentTime - lastShownTime >= 24 * 60 * 60 * 1000) {
            showPopup();
            setLastShownTime();
        }

    // Attach click event listeners to emojis
    const emojis = document.querySelectorAll('.emoji');
    emojis.forEach(emoji => {
        emoji.addEventListener('click', () => handleEmojiClick(emoji.textContent));
    });
});
//END PreOpen Popup

//Request
function openEmailModal() {
    $('#emailModal').modal('show');
}

function sendWhatsApp() {
    var phoneNumber = '+9647702581519';
    var message = encodeURIComponent("I'd like to request a new thing.");
    window.location.href = 'https://wa.me/' + phoneNumber + '?text=' + message;
}

function sendTelegram() {
    var username = 'MTAG96';
    var message = encodeURIComponent("I'd like to request a new thing.");
    window.location.href = 'https://telegram.me/' + username + '?text=' + message;
}

function sendEmailRequest() {
    // Fetch form data
    var formData = new FormData(document.getElementById('emailForm'));
    // Append file data to FormData object
    var fileInput = document.getElementById('attachment');
    if (fileInput.files.length > 0) {
        formData.append('attachment', fileInput.files[0]);
    }
    // Send AJAX request
    $.ajax({
        url: 'send_email.php',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        success: function(response) {
            // Handle the response here
            console.log(response);
            // Display confirmation message
            alert(response);

            // Reset form inputs
            $('#emailForm')[0].reset();

            // Close the modal
            $('#emailModal, #requestModal').modal('hide');
        },
        error: function(xhr, status, error) {
            // Handle errors here
            console.error(xhr.responseText);
            // Optionally, display an error message
            alert("Failed to send email.");
        }
    });
}

document.getElementById('attachment').addEventListener('change', function() {
var fileName = this.files[0].name;
document.getElementById('file-chosen').textContent = fileName;
});
//END Request