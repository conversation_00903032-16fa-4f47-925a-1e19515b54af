<?php
// Ensure config.php is loaded for URL helper functions
if (!function_exists('assets_url')) {
    if (file_exists('../config.php')) {
        require_once '../config.php';
    } elseif (file_exists('config.php')) {
        require_once 'config.php';
    }
}
?>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>
        <?php
        // Define an array to map page names to their corresponding titles
        $page_titles = array(
            'home' => 'سەرەکی',
            'school-guidance' => 'ڕێنمایی قوتابخانە',
            'awareness' => 'هۆشیاری',
            'online-test' => 'تێستی دەروونی',
            'counseling' => 'ڕاوێژکاری',
            'find-center' => 'سەنتەری دەروونی',
            'about' => 'دەربارەی ئێمە',
            'student' => 'قوتابی',
            'teacher' => 'مامۆستا',
            'talk-to-me' => 'قسەم لەگەڵ بکە',
            'contact' => 'پەیوەندی ڕاستەوخۆ',
            // Add more pages and titles as needed
        );

        // Set the default title
        $title = 'ئیلهامبەخشی دەروونی';

        // Check if a page parameter is provided in the URL
        if(isset($_GET['page']) && isset($page_titles[$_GET['page']])) {
            // Sanitize the input to prevent XSS attacks
            $page = htmlspecialchars($_GET['page']);

            // Set the title based on the page
            $title = $page_titles[$page];
        }
        echo $title;
        ?>
    </title>
    <link rel="manifest" href="../manifest.json">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://getbootstrap.com/docs/5.3/assets/css/docs.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-1.10.2.js"></script>
    <?php if (function_exists('assets_url')): ?>
        <link href="<?php echo assets_url('css/style.css'); ?>" rel="stylesheet">
        <link rel="icon" type="image/x-icon" href="<?php echo url('img/fav.ico'); ?>">
    <?php else: ?>
        <link href="../assets/css/style.css" rel="stylesheet">
        <link rel="icon" type="image/x-icon" href="../img/fav.ico">
    <?php endif; ?>
</head>
