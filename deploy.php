<?php
/**
 * Deployment Configuration Script
 * Switches between localhost and production configurations
 */

// Configuration settings
$configs = [
    'localhost' => [
        'rewrite_base' => '/inspiremental/',
        'db_host' => 'localhost',
        'db_user' => 'root',
        'db_pass' => '',
        'db_name' => 'inspiremental',
        'base_url' => 'http://localhost/inspiremental/',
        'admin_url' => 'http://localhost/inspiremental/admin/',
        'api_url' => 'http://localhost/inspiremental/api/'
    ],
    'production' => [
        'rewrite_base' => '/',
        'db_host' => 'localhost',
        'db_user' => 'yaridagr_inspiremental',
        'db_pass' => '.y$4Tx}25x7&',
        'db_name' => 'yaridagr_inspiremental',
        'base_url' => 'https://inspiremental.org/',
        'admin_url' => 'https://inspiremental.org/admin/',
        'api_url' => 'https://inspiremental.org/api/'
    ]
];

// Get environment from command line or default to localhost
$environment = isset($argv[1]) ? $argv[1] : 'localhost';

if (!isset($configs[$environment])) {
    die("Error: Unknown environment '$environment'. Use 'localhost' or 'production'.\n");
}

$config = $configs[$environment];

echo "Configuring for: $environment\n";

// Update .htaccess file
$htaccess_content = "RewriteEngine On

# Configuration for $environment
RewriteBase {$config['rewrite_base']}

# Redirect base URL to home page
RewriteRule ^$ home [R=301,L]

# Redirect old index.php URLs to clean URLs
RewriteCond %{THE_REQUEST} \s/+[^/\s]*\.php\?page=([^\s&]+) [NC]
RewriteRule ^ %1? [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing  
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Main page routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d";

if ($environment === 'localhost') {
    $htaccess_content .= "
RewriteCond %{REQUEST_URI} !^/inspiremental/admin/
RewriteCond %{REQUEST_URI} !^/inspiremental/api/
RewriteCond %{REQUEST_URI} !^/inspiremental/assets/
RewriteCond %{REQUEST_URI} !^/inspiremental/uploads/
RewriteCond %{REQUEST_URI} !^/inspiremental/img/
RewriteCond %{REQUEST_URI} !^/inspiremental/font/
RewriteCond %{REQUEST_URI} !^/inspiremental/guide/";
} else {
    $htaccess_content .= "
RewriteCond %{REQUEST_URI} !^/admin/
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_URI} !^/assets/
RewriteCond %{REQUEST_URI} !^/uploads/
RewriteCond %{REQUEST_URI} !^/img/
RewriteCond %{REQUEST_URI} !^/font/
RewriteCond %{REQUEST_URI} !^/guide/";
}

$htaccess_content .= "
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
</IfModule>

# Prevent access to sensitive files
<Files ~ \"^\\.\">
    Order allow,deny
    Deny from all
</Files>

<Files ~ \"\\.sql$\">
    Order allow,deny
    Deny from all
</Files>
";

// Write .htaccess file
file_put_contents('.htaccess', $htaccess_content);
echo "✅ Updated .htaccess for $environment\n";

// Update database configuration
$db_content = "<?php
// Configuration for $environment
\$servername = \"{$config['db_host']}\";
\$username = \"{$config['db_user']}\";
\$password = \"{$config['db_pass']}\";
\$dbname = \"{$config['db_name']}\";

// Create connection
\$con = new mysqli(\$servername, \$username, \$password, \$dbname);

// Check connection
if (\$con->connect_error) {
    die(\"Connection failed: \" . \$con->connect_error);
}

// Set character set to avoid encoding issues
\$con->set_charset(\"utf8mb4\");
?>";

file_put_contents('db.php', $db_content);
echo "✅ Updated database configuration for $environment\n";

// Create config.php with environment settings
$config_content = "<?php
// Environment configuration
define('ENVIRONMENT', '$environment');
define('BASE_URL', '{$config['base_url']}');
define('ADMIN_URL', '{$config['admin_url']}');
define('API_URL', '{$config['api_url']}');

// Helper functions
function url(\$path = '') {
    return BASE_URL . ltrim(\$path, '/');
}

function admin_url(\$path = '') {
    return ADMIN_URL . ltrim(\$path, '/');
}

function api_url(\$path = '') {
    return API_URL . ltrim(\$path, '/');
}
?>";

file_put_contents('config.php', $config_content);
echo "✅ Created config.php for $environment\n";

echo "\n🎉 Configuration complete!\n";
echo "Environment: $environment\n";
echo "Base URL: {$config['base_url']}\n";
echo "Admin URL: {$config['admin_url']}\n";
echo "API URL: {$config['api_url']}\n";

if ($environment === 'localhost') {
    echo "\n📝 Test URLs:\n";
    echo "- Home: http://localhost/inspiremental/home\n";
    echo "- About: http://localhost/inspiremental/about\n";
    echo "- Admin: http://localhost/inspiremental/admin\n";
    echo "- Online Test: http://localhost/inspiremental/online-test\n";
}
?>
