<style>
        .container {
            margin-top: 10rem;
            width: 90%;
            min-width: 300px;
            max-width: 1111px;
            background: #fff;
            padding: 0;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            height: 50vh;
            padding-top: 20px !important;
        }
        .progress-bar {
            height: 10px;
            background: #ddd;
            border-radius: 0;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .progress-bar .progress {
            border-radius: 0;
            height: 100%;
            background: #ff8c00;
            width: 0%;
            transition: width 0.3s ease;
        }
        .testTitle {
            text-align: center;
            background: #265e6d;
            color: gainsboro;
            padding: 10px 20px;
            margin-top: -25px;
            margin-bottom: 0px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }
        .question-container {
            font-size: larger;
            position: relative;
            width: 100%;
            height: calc(100% - 111px);
            overflow: hidden;
            text-align: center;
        }
        .question {
            background-color: rgba(255, 255, 255, 0.75);
            z-index: 100;
            font-size: 1.2em;
            text-align: center;
            position: absolute;
            padding: 0 20px;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: transform 0.5s ease, opacity 0.5s ease;
        }
        .options {
            width: 100%;
            margin-top: 20px;
            position: absolute;
            bottom: 25%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: row;
            gap: 10px;
            transition: transform 0.5s ease, opacity 0.5s ease;
        }
        .options button {
            display: inline-block;
            width: 18%;
            padding: 20px 10px;
            font-size: 1em;
            color: dimgray;
            background:rgba(0, 0, 0, 0.05);
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .options button:hover {
            background: #265e6d;
            color: white;
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            margin: -15px 0 0 0;
        }
        .navigation button {
            padding: 10px 20px;
            font-size: 1em;
            width: 100px;
            background: #265e6d;
            color: white;
            border: none;
            cursor: pointer;
        }
        #next {
            border-radius: 0 10px 0 0;
            position: absolute;
            bottom: 0;
            left: 0;
        }
        #prev {
            border-radius: 10px 0 0 0;
            position: absolute;
            bottom: 0;
            right: 0;
        }
        #slide {
            padding: 1rem;
            margin: 0 auto;
            color: gray;
        }
        .navigation button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        @media (max-width: 576px) {
            .container {height: 75vh; margin-top: 5rem;}
            .options {flex-direction: column; bottom: 30px;}
            .options button {width: 75%; padding: 10px;}
            .question-container {font-size: large;}
            .navigation {margin: 15px 0 0 0;}
        }
        @media (max-width: 320px) {
            .container {height: 85vh; margin-top: 4rem;}
            .question-container {font-size: small;}
        }
    </style>
</head>
<body>
    <div class="container">
        <h3 class="testTitle">Your Mental Health Today Test</h3>
        <div class="progress-bar">
            <div class="progress" id="progress"></div>
        </div>
        <div class="question-container" id="question-container">
            <!-- Questions will be dynamically inserted here -->
        </div>
        <div class="navigation">
            <button id="prev" disabled>Previous</button>
            <p id="slide">Slide 1 of 10</p>
            <button id="next" disabled>Next</button>
        </div>
    </div>

    <script>
    const questions = [
        "I've had trouble focusing on things like reading, TV shows, or conversations.",
        "I often feel restless or uneasy without knowing why.",
        "I have experienced sudden feelings of fear or panic.",
        "I avoid certain places or situations because they make me feel anxious.",
        "I find myself re-living past traumatic events in my mind.",
        "I feel disconnected from people around me.",
        "I struggle to control feelings of anger or frustration.",
        "I often have trouble falling asleep or staying asleep.",
        "I feel constantly on edge or hyper-aware of my surroundings.",
        "I experience difficulty trusting others." 
    ];

    const scores = Array(questions.length).fill(null);
    let currentQuestion = 0;
    let highestReachedQuestion = 0;

    const progressElement = document.getElementById("progress");
    const questionContainer = document.getElementById("question-container");
    const prevButton = document.getElementById("prev");
    const nextButton = document.getElementById("next");
    const slideCounter = document.getElementById("slide");

    function renderQuestion() {
        // Clear existing question and options
        questionContainer.innerHTML = "";

        // Create question element
        const questionElement = document.createElement("div");
        questionElement.classList.add("question");
        questionElement.textContent = questions[currentQuestion];
        questionElement.style.transform = "translateX(100%)";
        questionContainer.appendChild(questionElement);

        // Create options
        const optionsElement = document.createElement("div");
        optionsElement.classList.add("options");
        ["Not at all", "Once", "A few times", "Several times", "Always"].forEach((option, index) => {
            const button = document.createElement("button");
            button.textContent = option;
            button.onclick = () => selectAnswer(index);
            optionsElement.appendChild(button);
        });
        optionsElement.style.transform = "translateX(100%)";
        questionContainer.appendChild(optionsElement);

        // Trigger sliding animation
        setTimeout(() => {
            questionElement.style.transform = "translateX(0)";
            optionsElement.style.transform = "translateX(0)";
        }, 10);

        // Update progress bar
        progressElement.style.width = `${((currentQuestion + 1) / questions.length) * 100}%`;

        // Update navigation buttons and slide counter
        prevButton.disabled = currentQuestion === 0;
        nextButton.disabled = scores[currentQuestion] === null;
        slideCounter.textContent = `Slide ${currentQuestion + 1} of ${questions.length}`;
    }

    function slideOut(direction, callback) {
        const questionElement = questionContainer.querySelector(".question");
        const optionsElement = questionContainer.querySelector(".options");

        const outgoingOffset = direction === "left" ? "100%" : "-100%";
        const incomingOffset = direction === "left" ? "-100%" : "100%";

        // Slide out current question
        questionElement.style.transform = `translateX(${outgoingOffset})`;
        optionsElement.style.transform = `translateX(${outgoingOffset})`;

        setTimeout(() => {
            // Execute the callback to update the question index
            callback();

            // Slide in the new question
            const newQuestionElement = questionContainer.querySelector(".question");
            const newOptionsElement = questionContainer.querySelector(".options");

            newQuestionElement.style.transform = `translateX(${incomingOffset})`;
            newOptionsElement.style.transform = `translateX(${incomingOffset})`;

            setTimeout(() => {
                newQuestionElement.style.transform = "translateX(0)";
                newOptionsElement.style.transform = "translateX(0)";
            }, 10);
        }, 500);
    }

    function selectAnswer(index) {
        scores[currentQuestion] = index;

        // Update the highest reached question
        if (currentQuestion >= highestReachedQuestion) {
            highestReachedQuestion = currentQuestion + 1;
        }

        nextButton.disabled = false;

        if (currentQuestion < questions.length - 1) {
            slideOut("left", () => {
                currentQuestion++;
                renderQuestion();
            });
        } else {
            showResults();
        }
    }

    prevButton.onclick = () => {
        if (currentQuestion > 0) {
            slideOut("right", () => {
                currentQuestion--;
                renderQuestion();
            });
        }
    };

    nextButton.onclick = () => {
        // Determine the slide direction
        const direction = currentQuestion + 1 > highestReachedQuestion ? "right" : "left";

        if (currentQuestion < questions.length - 1) {
            slideOut(direction, () => {
                currentQuestion++;
                if (currentQuestion > highestReachedQuestion) {
                    highestReachedQuestion = currentQuestion;
                }
                renderQuestion();
            });
        } else {
            showResults();
        }
    };

    function showResults() {
        slideOut("left", () => {
            questionContainer.innerHTML = "<h2>Test Complete!</h2>";
            const resultText = document.createElement("p");
            const totalScore = scores.reduce((a, b) => a + b, 0);
            resultText.textContent = `Your total score is: ${totalScore}`;
            resultText.style.transform = "translateX(100%)";
            questionContainer.appendChild(resultText);

            setTimeout(() => {
                resultText.style.transform = "translateX(0)";
            }, 10);
        });
    }

    // Initial render
    renderQuestion();
</script>
