<?php
// Check if the form was submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Fetch form data
    $section = $_POST['section'];
    $email = $_POST['email'];
    $subject = $_POST['subject'];
    $message = $_POST['message'];

    // File upload handling
    $attachment = $_FILES['attachment'];
    $attachment_name = $attachment['name'];
    $attachment_tmp_name = $attachment['tmp_name'];

    // Debugging: Log file details
    error_log("Attachment Name: " . $attachment_name);
    error_log("Attachment Temp Name: " . $attachment_tmp_name);

    // Create email headers
    $headers = "From: " . $email . "\r\n";
    $headers .= "Reply-To: " . $email . "\r\n";
    $headers .= "Content-Type: multipart/mixed; boundary=\"boundary\"\r\n";

    // Add your email address where you want to receive the email
    $to = "<EMAIL>"; // Change this to your email address

    // Construct email message
    $body = "--boundary\r\n";
    $body .= "Content-Type: text/plain; charset=UTF-8\r\n";
    $body .= "\r\n";
    $body .= $message . "\r\n";
    $body .= "--boundary\r\n";
    $body .= "Content-Type: application/octet-stream; name=\"" . $attachment_name . "\"\r\n";
    $body .= "Content-Transfer-Encoding: base64\r\n";
    $body .= "Content-Disposition: attachment; filename=\"" . $attachment_name . "\"\r\n";
    $body .= "\r\n";
    $body .= chunk_split(base64_encode(file_get_contents($attachment_tmp_name))) . "\r\n";
    $body .= "--boundary--\r\n";

    // Send email with attachment
    if (mail($to, $subject, $body, $headers)) {
        echo "Email sent successfully!";
    } else {
        echo "Failed to send email.";
    }
} else {
    // If the request method is not POST, redirect back to the form
    header("Location: index.php");
    exit;
}
?>