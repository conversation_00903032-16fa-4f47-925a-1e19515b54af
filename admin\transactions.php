<?php
session_start();
include '../config.php'; // This will include db.php automatically

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

if ($_SESSION['role'] !== 'Admin') {
    header('Location: ../index.php?page=home');
    exit();
}

// Check if transactions table exists
$table_check = $con->query("SHOW TABLES LIKE 'transactions'");
$transactions_exist = $table_check->num_rows > 0;

$transactions = [];
if ($transactions_exist) {
    // Fetch transactions from the database
    $result = $con->query("
        SELECT t.*, u.username 
        FROM transactions t 
        LEFT JOIN users u ON t.user_id = u.id 
        ORDER BY t.created_at DESC
    ");
    if ($result) {
        $transactions = $result->fetch_all(MYSQLI_ASSOC);
    }
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>بەڕێوەبردنی مامەڵەکان - ئیلهامبەخشی دەروونی</title>
    <?php include '../includes/head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(../font/Rabar_22.ttf);
        }
        body {
            font-family: Rabar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding-top: 0;
        }
        .admin-container {
            padding: 2rem 0;
            min-height: 100vh;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .table {
            font-family: Rabar;
        }
        .table th {
            background: linear-gradient(135deg, #265e6d, #10af9c);
            color: white;
            border: none;
            font-weight: 600;
        }
        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-completed { background: #28a745; color: white; }
        .status-pending { background: #ffc107; color: #212529; }
        .status-failed { background: #dc3545; color: white; }
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <?php include '../includes/admin_navbar.php'; ?>
    
    <div class="admin-container">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="admin-card">
                        <h2 class="mb-4">بەڕێوەبردنی مامەڵەکان</h2>
                        
                        <?php if (!$transactions_exist): ?>
                            <div class="empty-state">
                                <i class="fas fa-info-circle"></i>
                                <h4>خشتەی مامەڵەکان دروست نەکراوە</h4>
                                <p>تا ئێستا هیچ مامەڵەیەک تۆمار نەکراوە لە سیستەمەکەدا.</p>
                            </div>
                        <?php elseif (empty($transactions)): ?>
                            <div class="empty-state">
                                <i class="fas fa-receipt"></i>
                                <h4>هیچ مامەڵەیەک نییە</h4>
                                <p>تا ئێستا هیچ مامەڵەیەک ئەنجام نەدراوە.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>بەکارهێنەر</th>
                                            <th>بڕ</th>
                                            <th>جۆر</th>
                                            <th>دۆخ</th>
                                            <th>بەروار</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($transactions as $transaction): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($transaction['username'] ?? 'نەناسراو'); ?></td>
                                            <td><?php echo number_format($transaction['amount'] ?? 0); ?> دینار</td>
                                            <td><?php echo htmlspecialchars($transaction['type'] ?? 'نەناسراو'); ?></td>
                                            <td>
                                                <?php 
                                                $status = $transaction['status'] ?? 'pending';
                                                $status_class = 'status-pending';
                                                $status_text = 'چاوەڕوان';
                                                
                                                if ($status == 'completed') {
                                                    $status_class = 'status-completed';
                                                    $status_text = 'تەواو';
                                                } elseif ($status == 'failed') {
                                                    $status_class = 'status-failed';
                                                    $status_text = 'سەرنەکەوتوو';
                                                }
                                                ?>
                                                <span class="status-badge <?php echo $status_class; ?>">
                                                    <?php echo $status_text; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y/m/d H:i', strtotime($transaction['created_at'] ?? 'now')); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../includes/script.php'; ?>
</body>
</html>
