<?php
/**
 * Console Status Checker
 * Shows current console error status and provides solutions
 */

echo "<h1>🖥️ Console Status Checker</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; border-radius: 5px; }
    .warning { color: orange; background: #fff8f0; padding: 10px; border-radius: 5px; }
    .error { color: red; background: #f8f0f0; padding: 10px; border-radius: 5px; }
    .info { color: blue; background: #f0f0f8; padding: 10px; border-radius: 5px; }
    .code { background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; }
    .section { margin: 20px 0; }
</style>";

echo "<div class='section'>";
echo "<h2>🔍 Current Error Status</h2>";

// Check if error filtering is implemented
$jsFile = 'assets/js/script.js';
$hasErrorFiltering = false;
if (file_exists($jsFile)) {
    $jsContent = file_get_contents($jsFile);
    if (strpos($jsContent, 'googleads.g.doubleclick.net') !== false) {
        $hasErrorFiltering = true;
        echo "<div class='success'>✅ Error filtering is active</div>";
    } else {
        echo "<div class='warning'>⚠️ Error filtering not implemented</div>";
    }
} else {
    echo "<div class='error'>❌ JavaScript file not found</div>";
}

// Check YouTube optimization
$awarenessFile = 'awareness_modal.php';
$hasYouTubeOptimization = false;
if (file_exists($awarenessFile)) {
    $awarenessContent = file_get_contents($awarenessFile);
    if (strpos($awarenessContent, 'youtube-nocookie.com') !== false) {
        $hasYouTubeOptimization = true;
        echo "<div class='success'>✅ YouTube embeds optimized</div>";
    } else {
        echo "<div class='warning'>⚠️ YouTube embeds not optimized</div>";
    }
} else {
    echo "<div class='info'>ℹ️ No YouTube embeds found</div>";
}

// Check for development files
$devFiles = ['setup-database.php', 'diagnostic.php', 'test-redirect.php'];
$foundDevFiles = [];
foreach ($devFiles as $file) {
    if (file_exists($file)) {
        $foundDevFiles[] = $file;
    }
}

if (empty($foundDevFiles)) {
    echo "<div class='success'>✅ No development files found</div>";
} else {
    echo "<div class='warning'>⚠️ Found " . count($foundDevFiles) . " development files</div>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 Expected Console Behavior</h2>";
echo "<div class='info'>";
echo "<h3>✅ Normal (Harmless) Errors:</h3>";
echo "<ul>";
echo "<li><strong>Google Ads:</strong> <code>googleads.g.doubleclick.net</code> - Blocked by ad blockers</li>";
echo "<li><strong>YouTube Ads:</strong> <code>pagead/id</code> - Normal ad blocking</li>";
echo "<li><strong>WebSocket:</strong> <code>ws://127.0.0.1:5500</code> - Development tools only</li>";
echo "</ul>";
echo "</div>";

echo "<div class='warning'>";
echo "<h3>⚠️ Errors to Investigate:</h3>";
echo "<ul>";
echo "<li>Database connection errors</li>";
echo "<li>404 errors for missing files</li>";
echo "<li>JavaScript syntax errors</li>";
echo "<li>CORS errors for API calls</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🛠️ Console Testing</h2>";
echo "<p>Open your browser's Developer Tools (F12) and check the Console tab:</p>";

echo "<div class='code'>";
echo "<strong>How to test:</strong><br>";
echo "1. Press F12 to open Developer Tools<br>";
echo "2. Click on the 'Console' tab<br>";
echo "3. Refresh the page<br>";
echo "4. Look for error messages<br>";
echo "5. Check if Google ads errors are filtered out";
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Quick Actions</h2>";
echo "<ul>";
if (!$hasErrorFiltering) {
    echo "<li><strong>Implement Error Filtering:</strong> Update assets/js/script.js with error filtering code</li>";
}
if (!$hasYouTubeOptimization) {
    echo "<li><strong>Optimize YouTube:</strong> Switch to youtube-nocookie.com in awareness_modal.php</li>";
}
if (!empty($foundDevFiles)) {
    echo "<li><a href='cleanup-dev-files.php'>🧹 Clean up development files</a></li>";
}
echo "<li><a href='diagnostic.php'>🔧 Run full diagnostic</a></li>";
echo "<li><a href='index.php'>🏠 Go to homepage</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📝 Implementation Status</h2>";
$totalChecks = 3;
$passedChecks = 0;
if ($hasErrorFiltering) $passedChecks++;
if ($hasYouTubeOptimization) $passedChecks++;
if (empty($foundDevFiles)) $passedChecks++;

$percentage = round(($passedChecks / $totalChecks) * 100);

if ($percentage >= 80) {
    echo "<div class='success'>";
    echo "<h3>🎉 Excellent! ($percentage% complete)</h3>";
    echo "<p>Your console error handling is well implemented. Most harmless errors should be filtered out.</p>";
} elseif ($percentage >= 60) {
    echo "<div class='warning'>";
    echo "<h3>👍 Good! ($percentage% complete)</h3>";
    echo "<p>Most optimizations are in place. Consider implementing the remaining suggestions.</p>";
} else {
    echo "<div class='error'>";
    echo "<h3>🔧 Needs Work ($percentage% complete)</h3>";
    echo "<p>Several optimizations can be implemented to improve console cleanliness.</p>";
}
echo "</div>";
echo "</div>";

echo "<p><strong>💡 Remember:</strong> Some console errors are completely normal and don't affect website functionality!</p>";
echo "<p><strong>⚠️ Security Note:</strong> Delete this file after checking console status!</p>";
?>
