//Request

function openEmailModal() {
  $("#emailModal").modal("show");
}

function sendWhatsApp() {
  var phoneNumber = "+9647702581519";
  var message = encodeURIComponent("I'd like to request a new thing.");
  window.location.href = "https://wa.me/" + phoneNumber + "?text=" + message;
}

function sendTelegram() {
  var username = "MTAG96";
  var message = encodeURIComponent("I'd like to request a new thing.");
  window.location.href = "https://telegram.me/" + username + "?text=" + message;
}

function sendEmailRequest() {
  // Fetch form data
  var formData = new FormData(document.getElementById("emailForm"));
  // Append file data to FormData object
  var fileInput = document.getElementById("attachment");
  if (fileInput.files.length > 0) {
    formData.append("attachment", fileInput.files[0]);
  }
  // Send AJAX request
  $.ajax({
    url: "send_email.php",
    type: "POST",
    data: formData,
    contentType: false,
    processData: false,
    success: function (response) {
      // Handle the response here
      console.log(response);
      // Display confirmation message
      alert(response);

      // Reset form inputs
      $("#emailForm")[0].reset();

      // Close the modal
      $("#emailModal, #requestModal").modal("hide");
    },
    error: function (xhr, status, error) {
      // Handle errors here
      console.error(xhr.responseText);
      // Optionally, display an error message
      alert("Failed to send email.");
    },
  });
}

document.getElementById("attachment").addEventListener("change", function () {
  var fileName = this.files[0].name;
  document.getElementById("file-chosen").textContent = fileName;
});
//END Request

// Handle modal toggling for Register
document
  .getElementById("openRegisterModal")
  .addEventListener("click", function (e) {
    e.preventDefault();
    $("#loginModal").modal("hide");
    setTimeout(function () {
      $("#registerModal").modal("show");
    }, 300);
  });

// Handle login form submission
document.getElementById("loginForm").addEventListener("submit", function (e) {
  e.preventDefault();
  let formData = new FormData(this);

  fetch("login.php", {
    method: "POST",
    body: formData,
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.json(); // Expecting JSON response
    })
    .then((data) => {
      if (data.success) {
        window.location.href = data.redirect; // Redirect to the specified page
      } else {
        alert(data.message); // Show the error message from the server
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      alert("An error occurred during login. Please try again.");
    });
});

// Handle registration form submission
document
  .getElementById("registerForm")
  .addEventListener("submit", function (e) {
    e.preventDefault();
    let formData = new FormData(this);

    fetch("register.php", {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        return response.json(); // Expecting JSON response
      })
      .then((data) => {
        if (data.success) {
          alert(data.message); // Show success message
          $("#registerModal").modal("hide");
          setTimeout(function () {
            $("#loginModal").modal("show");
          }, 300);
        } else {
          alert(data.message); // Show the error message from the server
        }
      })
      .catch((error) => {
        console.error("Error:", error);
        alert("An error occurred during registration. Please try again.");
      });
  });

// Handle modal toggling for Forgot Password
document
  .getElementById("openForgotPasswordModal")
  .addEventListener("click", function (e) {
    e.preventDefault();
    $("#loginModal").modal("hide");
    setTimeout(function () {
      $("#forgotPasswordModal").modal("show");
    }, 300); // Small delay to avoid modal conflicts
  });

// Function to open the Profile Modal and load user data
const openProfileModalButton = document.getElementById("openProfileModal");
if (openProfileModalButton) {
  openProfileModalButton.addEventListener("click", function () {
    fetch("get_profile_data.php")
      .then((response) => response.json())
      .then((data) => {
        const profileUsername = document.getElementById("profileUsername");
        const profileEmail = document.getElementById("profileEmail");

        if (profileUsername) profileUsername.value = data.username;
        if (profileEmail) profileEmail.value = data.email;
      })
      .catch((error) => console.error("Error loading profile data:", error));

    $("#profileModal").modal("show");
  });
}

// Handle Profile Form submission
document.getElementById("profileForm").addEventListener("submit", function (e) {
  e.preventDefault();
  let formData = new FormData(this);

  fetch("update_profile.php", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert("Account updated successfully!");
        $("#profileModal").modal("hide");
      } else {
        alert("Failed to update account. " + (data.message || ""));
      }
    })
    .catch((error) => console.error("Error updating profile:", error));
});

// Preview selected profile photo
document
  .getElementById("profilePhoto")
  .addEventListener("change", function (e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        document.getElementById("profileImage").src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  });

// Toggle edit mode
document.getElementById("editButton").addEventListener("click", function () {
  const formInputs = document.querySelectorAll("#profileForm input");
  const updateButton = document.getElementById("updateButton");

  // Enable inputs for editing
  formInputs.forEach((input) => (input.disabled = !input.disabled));

  // Toggle Update button's disabled state
  updateButton.disabled = !updateButton.disabled;

  // Change Edit button text
  this.textContent = updateButton.disabled ? "گۆڕین" : "لابردن";
});

// Preview selected profile photo
document
  .getElementById("profilePhoto")
  .addEventListener("change", function (e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        document.getElementById("profileImage").src = `${
          e.target.result
        }?${new Date().getTime()}`;
      };
      reader.readAsDataURL(file);
    }
  });

//Schedule Starts Below

// Wait for the DOM to load
document.addEventListener("DOMContentLoaded", function () {
  const timeSlotContainer = document.getElementById("timeSlotContainer");
  const addTimeSlotButton = document.getElementById("addTimeSlot");
  const scheduleForm = document.getElementById("scheduleForm");
  const dayOfWeekSelect = document.getElementById("dayOfWeek");

  // Function to add a new time slot
  addTimeSlotButton.addEventListener("click", function () {
    addTimeSlot();
  });

  // Function to add a new or default time slot
  function addTimeSlot(start = "", end = "") {
    const timeSlot = document.createElement("div");
    timeSlot.classList.add("row", "time-slot", "mb-3", "align-items-end");
    timeSlot.innerHTML = `
            <div class="col-4">
                <label class="form-label">دەستپێکردن</label>
                <input type="time" class="form-control" name="start_time[]" value="${start}" required>
            </div>
            <div class="col-4">
                <label class="form-label">کۆتاییپێهێنان</label>
                <input type="time" class="form-control" name="end_time[]" value="${end}" required>
            </div>
            <div class="col-2">
                <button type="button" class="btn btn-danger remove-time-slot mt-2">لابردن</button>
            </div>
        `;
    timeSlotContainer.appendChild(timeSlot);
  }

  // Function to remove a time slot
  timeSlotContainer.addEventListener("click", function (event) {
    if (event.target.classList.contains("remove-time-slot")) {
      event.target.closest(".time-slot").remove();
    }
  });

  // Handle form submission
  scheduleForm.addEventListener("submit", function (event) {
    event.preventDefault();

    const formData = new FormData(scheduleForm);
    formData.append("day_of_week", dayOfWeekSelect.value); // Add selected day to form data

    fetch("update_schedule.php", {
      method: "POST",
      body: formData,
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          alert(data.message);
          // Optionally, reset the form and close the modal
          scheduleForm.reset();
          $("#scheduleModal").modal("hide");
        } else {
          alert(
            data.message || "An error occurred while updating the schedule."
          );
        }
      })
      .catch((error) => {
        console.error("Error:", error);
        alert("An error occurred while updating the schedule.");
      });
  });

  // When the modal is shown, load schedule for the selected day
  $("#scheduleModal").on("show.bs.modal", function () {
    loadScheduleData(dayOfWeekSelect.value);
  });

  // Fetch and display schedule data when the day of the week is changed
  dayOfWeekSelect.addEventListener("change", function () {
    loadScheduleData(this.value);
  });

  // Function to load existing schedule data for the selected day
  function loadScheduleData(dayOfWeek) {
    timeSlotContainer.innerHTML = ""; // Clear existing time slots

    fetch(`api/get_schedule.php?day_of_week=${encodeURIComponent(dayOfWeek)}`)
      .then((response) => response.json())
      .then((data) => {
        if (data.success && data.schedule.length > 0) {
          // Populate time slots with existing schedule data
          data.schedule.forEach((slot) => {
            addTimeSlot(slot.start_time, slot.end_time);
          });
        } else {
          // No schedule data, add a default time slot
          addTimeSlot();
        }
      })
      .catch((error) => {
        console.error("Error fetching schedule:", error);
        // In case of error, add a default time slot
        addTimeSlot();
      });
  }
});
document.addEventListener("DOMContentLoaded", function () {
  const weekScheduleBody = document.getElementById("weekScheduleBody");

  // Event listener for View Week Schedule button
  document
    .getElementById("viewWeekSchedule")
    .addEventListener("click", function () {
      loadWeeklySchedule();
    });

  // Function to load the entire week's schedule
  function loadWeeklySchedule() {
    fetch("api/get_week_schedule.php")
      .then((response) => response.json())
      .then((data) => {
        // Clear existing content
        weekScheduleBody.innerHTML = "";

        if (data.success) {
          // Populate the table with weekly schedule data
          data.weekSchedule.forEach((daySchedule) => {
            const row = document.createElement("tr");

            // Day of the week
            const dayCell = document.createElement("td");
            dayCell.textContent = daySchedule.day_of_week;
            row.appendChild(dayCell);

            // Time slots for the day
            const timesCell = document.createElement("td");
            if (daySchedule.time_slots.length > 0) {
              daySchedule.time_slots.forEach((slot) => {
                const slotDiv = document.createElement("div");
                slotDiv.textContent = `${slot.end_time} - ${slot.start_time}`;
                timesCell.appendChild(slotDiv);
              });
            } else {
              timesCell.textContent = "هیچ کاتێك نییە";
            }
            row.appendChild(timesCell);

            weekScheduleBody.appendChild(row);
          });
        } else {
          // Display a message if no schedule is available
          const row = document.createElement("tr");
          const cell = document.createElement("td");
          cell.setAttribute("colspan", "2");
          cell.classList.add("text-center");
          cell.textContent = "هیچ کاتەکانی هەفتە نییە";
          row.appendChild(cell);
          weekScheduleBody.appendChild(row);
        }
      })
      .catch((error) => {
        console.error("Error loading weekly schedule:", error);
        alert("هەڵەیەک ڕوویدا لە کاتی داگرتنی خشتەی هەفتانە.");
      });
  }
});

document.addEventListener("DOMContentLoaded", function () {
  // Event listener for schedule button
  document.querySelectorAll(".view-schedule-button").forEach((button) => {
    button.addEventListener("click", function () {
      const userId = this.getAttribute("data-user-id");
      const username = this.getAttribute("data-username"); // Retrieve username from data attribute

      // Set the modal title dynamically to the user's name
      document.getElementById(
        "userScheduleModalLabel"
      ).innerText = `خشتەی ${username}`;

      // Fetch and display the user's schedule
      fetch(`api/get_user_schedule.php?user_id=${userId}`)
        .then((response) => response.json())
        .then((data) => {
          const scheduleContent = document.getElementById("scheduleContent");
          scheduleContent.innerHTML = ""; // Clear previous content

          if (data.success && data.schedule.length > 0) {
            data.schedule.forEach((day) => {
              const daySchedule = `
                                <h6>${day.day_of_week}</h6>
                                <ul>
                                    ${day.slots
                                      .map(
                                        (slot) =>
                                          `<li>${slot.end_time} - ${slot.start_time}</li>`
                                      )
                                      .join("")}
                                </ul><hr>
                            `;
              scheduleContent.innerHTML += daySchedule;
            });
          } else {
            scheduleContent.innerHTML = "<p>خشتەکەی بەردەست نییە.</p>";
          }
        })
        .catch((error) => {
          console.error("Error fetching schedule:", error);
          document.getElementById("scheduleContent").innerHTML =
            "<p>کێشەیەک ڕویدا. تکایە دووبارە هەوڵ بدە.</p>";
        });
    });
  });
});

// ----- Add Credit Modals ----- //

// ----- Awareness Modals ----- //

document.addEventListener("DOMContentLoaded", () => {
  const container = document.querySelector(".awareMiniBox");

  if (container) {
    container.addEventListener("click", (event) => {
      const target = event.target.closest("a.awareLabel");

      if (target) {
        event.preventDefault(); // Prevent default anchor behavior
        const modalId = target.getAttribute("href").replace("#", ""); // Extract modal ID
        const modalElement = document.getElementById(`${modalId}Modal`); // Find modal element

        if (modalElement) {
          // Update the URL without reloading the page
          const newUrl = `${window.location.pathname}#${modalId}`;
          history.pushState(null, "", newUrl);

          // Show the modal
          const modalInstance = new bootstrap.Modal(modalElement);
          modalInstance.show();
        } else {
          console.error(`Modal with ID "${modalId}Modal" not found.`);
        }
      }
    });
  } else {
    console.error("Container '.awareMiniBox' not found.");
  }

  // Listen globally for modal close events
  document.addEventListener("hidden.bs.modal", () => {
    if (window.location.hash) {
      // Remove the hash from the URL
      history.pushState(null, "", window.location.pathname);
    }
  });
});

document
  .getElementById("openAddCreditModal")
  .addEventListener("click", function () {
    $("#addCreditModal").modal("show");
  });

document
  .getElementById("openScheduleModal")
  .addEventListener("click", function () {
    $("#scheduleModal").modal("show");
  });

document
  .getElementById("openRequestModal")
  .addEventListener("click", function () {
    $("#requestModal").modal("show");
  });
