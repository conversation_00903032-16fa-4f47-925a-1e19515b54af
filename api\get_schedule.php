<?php
session_start();
include '../db.php';

if (isset($_GET['day_of_week'])) {
    $user_id = $_SESSION['user_id'];
    $day_of_week = $_GET['day_of_week'];

    $stmt = $con->prepare("SELECT start_time, end_time FROM users_schedule WHERE user_id = ? AND day_of_week = ?");
    $stmt->bind_param("is", $user_id, $day_of_week);
    $stmt->execute();
    $result = $stmt->get_result();

    $schedule = [];
    while ($row = $result->fetch_assoc()) {
        $schedule[] = $row;
    }

    if (!empty($schedule)) {
        echo json_encode(['success' => true, 'schedule' => $schedule]);
    } else {
        echo json_encode(['success' => false, 'message' => 'No schedule found.']);
    }
}
?>
