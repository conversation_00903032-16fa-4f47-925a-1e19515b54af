<?php
session_start();
include '../db.php';
include '../config.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

if ($_SESSION['role'] !== 'Admin') {
    header('Location: ../index.php?page=home');
    exit();
}

// Get user ID from URL
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Fetch user data from the database
$query = "SELECT id, username, email, role FROM users WHERE id = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    echo "<div class='alert alert-danger'>بەکارهێنەر نەدۆزرایەوە.</div>";
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'];
    $email = $_POST['email'];
    $role = $_POST['role'];
    
    // Update user in database
    $update_query = "UPDATE users SET username = ?, email = ?, role = ? WHERE id = ?";
    $update_stmt = $con->prepare($update_query);
    $update_stmt->bind_param("sssi", $username, $email, $role, $user_id);
    
    if ($update_stmt->execute()) {
        $success_message = "بەکارهێنەر بە سەرکەوتوویی نوێکرایەوە.";
        // Refresh user data
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
    } else {
        $error_message = "هەڵەیەک ڕوویدا لە نوێکردنەوەی بەکارهێنەر.";
    }
}
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>دەستکاری بەکارهێنەر - ئیلهامبەخشی دەروونی</title>
    <?php include '../includes/head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(../font/Rabar_22.ttf);
        }
        body {
            font-family: Rabar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding-top: 0;
        }
        .admin-container {
            padding: 2rem 0;
            min-height: 100vh;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-family: Rabar;
        }
        .form-control:focus {
            border-color: #265e6d;
            box-shadow: 0 0 0 0.2rem rgba(38, 94, 109, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #265e6d, #10af9c);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        .btn-secondary {
            background: #6c757d;
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
    </style>
</head>
<body>
    <?php include '../includes/admin_navbar.php'; ?>
    
    <div class="admin-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="admin-card">
                        <h2 class="mb-4">دەستکاری بەکارهێنەر</h2>
                        
                        <?php if (isset($success_message)): ?>
                            <div class="alert alert-success"><?php echo $success_message; ?></div>
                        <?php endif; ?>
                        
                        <?php if (isset($error_message)): ?>
                            <div class="alert alert-danger"><?php echo $error_message; ?></div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">ناوی بەکارهێنەر</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($user['username']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">ئیمەیڵ</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="role" class="form-label">ڕۆڵ</label>
                                <select class="form-control" id="role" name="role" required>
                                    <option value="User" <?php echo $user['role'] == 'User' ? 'selected' : ''; ?>>بەکارهێنەر</option>
                                    <option value="Doctor" <?php echo $user['role'] == 'Doctor' ? 'selected' : ''; ?>>دکتۆر</option>
                                    <option value="Psychologist" <?php echo $user['role'] == 'Psychologist' ? 'selected' : ''; ?>>دەروونناس</option>
                                    <option value="Counsellor" <?php echo $user['role'] == 'Counsellor' ? 'selected' : ''; ?>>ڕاوێژکار</option>
                                    <option value="Admin" <?php echo $user['role'] == 'Admin' ? 'selected' : ''; ?>>بەڕێوەبەر</option>
                                </select>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    پاشەکەوتکردن
                                </button>
                                <a href="users.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    گەڕانەوە
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../includes/script.php'; ?>
</body>
</html>
