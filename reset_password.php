<?php
session_start();
include 'db.php';

if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['token']) && isset($_GET['email'])) {
    // Validate the token and check expiration
    $stmt = $con->prepare("SELECT * FROM users WHERE reset_token = ? AND email = ? AND token_expiry > NOW()");
    $stmt->bind_param("ss", $_GET['token'], $_GET['email']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 1) {
        // Token is valid; display the reset form
        echo '<form action="reset_password.php" method="post">
                <input type="hidden" name="email" value="'.htmlspecialchars($_GET['email']).'">
                <input type="hidden" name="token" value="'.htmlspecialchars($_GET['token']).'">
                <label for="new_password">Enter New Password:</label>
                <input type="password" id="new_password" name="new_password" placeholder="Enter new password" required>
                <button type="submit">Reset Password</button>
              </form>';
    } else {
        // Invalid or expired token
        echo "<script>alert('This password reset link is invalid or has expired.'); window.location.href = 'home';</script>";
    }
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Update the user's password
    $newPassword = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
    $stmt = $con->prepare("UPDATE users SET password = ?, reset_token = NULL, token_expiry = NULL WHERE email = ? AND reset_token = ?");
    $stmt->bind_param("sss", $newPassword, $_POST['email'], $_POST['token']);
    $stmt->execute();

    if ($stmt->affected_rows == 1) {
        // Password reset successful
        echo "<script>alert('Your password has been reset successfully.'); window.location.href = 'home';</script>";
    } else {
        // Password reset failed
        echo "<script>alert('Failed to reset password. Please try again.'); window.location.href = 'home';</script>";
    }
}
?>
