<?php
/**
 * Database Setup Script for Localhost
 * Run this once to set up the database on localhost
 */

// Only allow on localhost or command line
$host = $_SERVER['HTTP_HOST'] ?? '';
$isCommandLine = php_sapi_name() === 'cli';
$isLocalhost = strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false;

if (!$isCommandLine && !$isLocalhost) {
    die('This script only works on localhost or command line');
}

// Database configuration for localhost
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "inspiremental";

echo "<h2>Database Setup for Localhost</h2>";

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$servername;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to MySQL server</p>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
    echo "<p>✅ Database '$dbname' created/verified</p>";
    
    // Connect to the specific database
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute SQL file
    $sqlFile = 'inspiremental.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // Remove comments and split by semicolon
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore table already exists errors
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p>⚠️ Warning: " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                }
            }
        }
        
        echo "<p>✅ Database tables created/updated from $sqlFile</p>";
    } else {
        echo "<p>❌ SQL file '$sqlFile' not found</p>";
    }
    
    // Test the connection
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>✅ Database test successful - Found {$result['count']} users</p>";
    
    echo "<h3>✅ Setup Complete!</h3>";
    echo "<p>You can now access the website at: <a href='http://localhost/inspiremental/'>http://localhost/inspiremental/</a></p>";
    echo "<p><strong>Important:</strong> Delete this file (setup-database.php) after setup for security.</p>";
    
} catch (PDOException $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Make sure MySQL service is started</li>";
    echo "<li>Check if the database credentials are correct</li>";
    echo "</ul>";
}
?>
