<?php
/**
 * Database Configuration - Auto-detects environment
 * Works for both localhost and published website
 */

// Environment detection with flexible domain support
if (!function_exists('detectEnvironment')) {
    function detectEnvironment() {
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? '';
        $documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? '';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

        // Check for localhost/development environment
        if (
            strpos($host, 'localhost') !== false ||
            strpos($host, '127.0.0.1') !== false ||
            strpos($host, '::1') !== false ||
            strpos($documentRoot, 'xampp') !== false ||
            strpos($documentRoot, 'wamp') !== false ||
            strpos($documentRoot, 'htdocs') !== false ||
            strpos($documentRoot, 'laragon') !== false ||
            strpos($documentRoot, 'mamp') !== false
        ) {
            return 'localhost';
        }

        // Check for common production indicators
        if (
            strpos($documentRoot, 'public_html') !== false ||
            strpos($documentRoot, 'www') !== false ||
            strpos($documentRoot, 'htdocs') !== false ||
            strpos($scriptName, 'public_html') !== false ||
            isset($_SERVER['SERVER_ADMIN']) ||
            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on')
        ) {
            return 'production';
        }

        // If host has a proper domain (contains dot and not localhost)
        if (strpos($host, '.') !== false && strpos($host, 'localhost') === false) {
            return 'production';
        }

        // Default to localhost for unknown environments
        return 'localhost';
    }
}

// Get current environment
$environment = detectEnvironment();

// Database configurations for different environments
$configs = [
    'localhost' => [
        'servername' => 'localhost',
        'username' => 'root',
        'password' => '',
        'dbname' => 'inspiremental'
    ],
    'production' => [
        'servername' => $_ENV['DB_HOST'] ?? $_SERVER['DB_HOST'] ?? 'localhost',
        'username' => $_ENV['DB_USERNAME'] ?? $_SERVER['DB_USERNAME'] ?? 'yaridagr_inspiremental',
        'password' => $_ENV['DB_PASSWORD'] ?? $_SERVER['DB_PASSWORD'] ?? 'n*dMFX=i0-iq',
        'dbname' => $_ENV['DB_NAME'] ?? $_SERVER['DB_NAME'] ?? 'yaridagr_inspiremental'
    ]
];

// Check for custom database config file
if ($environment === 'production' && file_exists('db-config.php')) {
    $customConfig = include 'db-config.php';
    if (is_array($customConfig)) {
        $configs['production'] = array_merge($configs['production'], $customConfig);
    }
}

// Use the appropriate configuration
$config = $configs[$environment];

// Extract database credentials
$servername = $config['servername'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

// Create connection
$con = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($con->connect_error) {
    // Log error for debugging
    error_log("Production database connection failed: " . $con->connect_error);
    die("Database connection failed. Please contact the administrator.");
}

// Set character set to avoid encoding issues
$con->set_charset("utf8mb4");

// Set environment constants
define('CURRENT_ENVIRONMENT', $environment);

// Generate BASE_URL based on environment
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'] ?? ($environment === 'production' ? 'example.com' : 'localhost');

// Clean up the host if it contains path information
if (strpos($host, '/') !== false) {
    $host = explode('/', $host)[0];
}

// Set base URL with proper path detection
if ($environment === 'localhost') {
    // For localhost, detect the correct path
    $scriptPath = dirname($_SERVER['SCRIPT_NAME'] ?? '/inspiremental/index.php');
    $basePath = $scriptPath === '/' ? '/' : $scriptPath . '/';

    // Clean up any double slashes or dots
    $basePath = str_replace('//', '/', $basePath);
    $basePath = str_replace('/./', '/', $basePath);

    define('BASE_URL', $protocol . $host . $basePath);
} else {
    // For production, auto-detect the correct path
    $scriptPath = dirname($_SERVER['SCRIPT_NAME'] ?? '/index.php');

    // If the script is in the root directory, use root path
    if ($scriptPath === '/' || $scriptPath === '\\' || empty($scriptPath)) {
        $basePath = '/';
    } else {
        $basePath = $scriptPath . '/';
    }

    // Clean up any double slashes
    $basePath = str_replace('//', '/', $basePath);

    // Use HTTPS for production by default
    $productionProtocol = 'https://';

    define('BASE_URL', $productionProtocol . $host . $basePath);
}
?>