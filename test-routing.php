<?php
/**
 * Test URL Routing
 * Tests if clean URLs are working properly
 * DELETE AFTER TESTING
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 URL Routing Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #f8f0f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #f0f0f8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .test-link { display: inline-block; margin: 5px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; text-decoration: none; }
    .test-link:hover { background: #e9ecef; }
</style>";

echo "<div class='info'>";
echo "<h2>🔍 Testing Clean URL Routing</h2>";
echo "<p>This script tests if the .htaccess routing rules are working correctly.</p>";
echo "</div>";

// Test 1: Check .htaccess file
echo "<h3>1. .htaccess Configuration</h3>";
if (file_exists('.htaccess')) {
    $htaccess = file_get_contents('.htaccess');
    
    $rules_to_check = [
        'test-handler' => 'Test handler routing',
        'login' => 'Login routing',
        'register' => 'Register routing',
        'logout' => 'Logout routing',
        'admin' => 'Admin panel routing',
        'api' => 'API routing'
    ];
    
    foreach ($rules_to_check as $rule => $description) {
        if (strpos($htaccess, $rule) !== false) {
            echo "<p class='success'>✅ $description found in .htaccess</p>";
        } else {
            echo "<p class='error'>❌ $description missing from .htaccess</p>";
        }
    }
} else {
    echo "<p class='error'>❌ .htaccess file not found</p>";
}

// Test 2: Check target files exist
echo "<h3>2. Target Files Check</h3>";
$target_files = [
    'test-handler.php' => 'Test handler',
    'login.php' => 'Login page',
    'register.php' => 'Register page',
    'logout.php' => 'Logout page',
    'admin/index.php' => 'Admin panel',
    'api/send_email.php' => 'API endpoint example'
];

foreach ($target_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $description ($file) exists</p>";
    } else {
        echo "<p class='error'>❌ $description ($file) missing</p>";
    }
}

// Test 3: URL Test Links
echo "<h3>3. URL Test Links</h3>";
echo "<p>Click these links to test if clean URLs work:</p>";

$test_urls = [
    'test-handler?test_id=1' => 'Test Handler with parameter',
    'test-handler' => 'Test Handler without parameter',
    'login' => 'Login page',
    'register' => 'Register page',
    'admin' => 'Admin panel',
    'admin/users' => 'Admin users page',
    'api/send_email' => 'API endpoint',
    'home' => 'Homepage',
    'about' => 'About page',
    'online-test' => 'Online test page'
];

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin: 20px 0;'>";
foreach ($test_urls as $url => $description) {
    echo "<a href='$url' class='test-link' target='_blank'>";
    echo "<strong>$description</strong><br>";
    echo "<small>/$url</small>";
    echo "</a>";
}
echo "</div>";

// Test 4: JavaScript fetch test
echo "<h3>4. JavaScript Fetch Test</h3>";
echo "<p>This tests if AJAX calls to clean URLs work:</p>";
echo "<button onclick='testFetch()' class='btn btn-primary'>Test AJAX to test-handler</button>";
echo "<div id='fetch-result' style='margin-top: 10px;'></div>";

echo "<script>
function testFetch() {
    const resultDiv = document.getElementById('fetch-result');
    resultDiv.innerHTML = '<p>Testing...</p>';
    
    fetch('test-handler?test_id=1')
        .then(response => {
            if (response.ok) {
                resultDiv.innerHTML = '<p style=\"color: green;\">✅ AJAX to test-handler successful!</p>';
            } else {
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ AJAX failed with status: ' + response.status + '</p>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ AJAX error: ' + error.message + '</p>';
        });
}
</script>";

// Test 5: Server information
echo "<h3>5. Server Information</h3>";
echo "<p><strong>Server:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>";
echo "<p><strong>Query String:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'None') . "</p>";

// Test 6: Recommendations
echo "<h3>6. Troubleshooting</h3>";
echo "<div class='info'>";
echo "<h4>If clean URLs are not working:</h4>";
echo "<ol>";
echo "<li><strong>Check mod_rewrite:</strong> Ensure mod_rewrite is enabled on your server</li>";
echo "<li><strong>Check .htaccess permissions:</strong> Should be readable by web server</li>";
echo "<li><strong>Check server configuration:</strong> Some servers require AllowOverride All</li>";
echo "<li><strong>Test direct URLs:</strong> Try accessing test-handler.php directly first</li>";
echo "</ol>";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>Expected Behavior:</h4>";
echo "<ul>";
echo "<li><code>/test-handler?test_id=6</code> should work the same as <code>/test-handler.php?test_id=6</code></li>";
echo "<li><code>/login</code> should work the same as <code>/login.php</code></li>";
echo "<li><code>/admin</code> should work the same as <code>/admin/index.php</code></li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>⚠️ Security Warning</h3>";
echo "<p><strong>DELETE THIS FILE AFTER TESTING!</strong></p>";
echo "</div>";
?>
