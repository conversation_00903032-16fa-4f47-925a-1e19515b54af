<?php
session_start();
require('db.php'); // Include your database connection

// Check if the email is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email'])) {
    $email = mysqli_real_escape_string($con, $_POST['email']);
    
    // Check if the email exists in the database
    $query = "SELECT * FROM `users` WHERE email='$email'";
    $result = mysqli_query($con, $query);
    
    if (mysqli_num_rows($result) == 1) {
        // Generate a unique reset token and expiration time
        date_default_timezone_set('Asia/Baghdad');
        $token = bin2hex(random_bytes(50));
        $expiry = date('Y-m-d H:i:s', strtotime('+1 hour')); // Token expires in 1 hour and formatted for MySQL DATETIME

        // Store the token and expiry in the database
        $update_query = "UPDATE `users` SET reset_token='$token', token_expiry='$expiry' WHERE email='$email'";
        mysqli_query($con, $update_query);

        // Create the reset link (switch between localhost and published website)
        // $reset_link = "https://inspiremental.org/reset_password.php?token=$token&email=$email";
        $reset_link = "http://localhost/inspiremental/reset_password.php?token=$token&email=$email";

        // Email subject and message
        $subject = "Password Reset Request";
        $message = "Please click the following link to reset your password:\n\n$reset_link\n\nThis link will expire in 1 hour.";
        $headers = "From: <EMAIL>";

        // Send the reset email
        if (mail($email, $subject, $message, $headers)) {
            echo "<script>alert('A password reset link has been sent to your email address.');</script>";
            echo "<script>window.location.href = 'home';</script>"; // Redirect to home page after alert
        } else {
            echo "<script>alert('Failed to send the password reset email. Please try again.');</script>";
        }
    } else {
        echo "<script>alert('No account found with that email address.');</script>";
    }
}
?>