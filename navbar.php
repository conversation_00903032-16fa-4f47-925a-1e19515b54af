<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="home">
            <img src="<?php echo url('img/logo.png'); ?>" alt="" height="50px" class="" style="margin-right: -20px !important;">
            <p class="d-inline h5 align-middle pe-1" style="font-family: RabarBold !important;">ئیلهامبەخشی دەروونی</p>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mt-3 mt-md-0 ms-md-auto ms-0 mb-2 mb-lg-0 px-0 ps-md-0 pe-md-3">
                <!-- Your existing nav items -->
                <li class="d-block d-md-none nav-item<?php if(isset($_GET['page']) && $_GET['page'] == 'home') echo ' active'; ?>">
                    <a class="nav-link centered" aria-current="page" href="home">سەرەکی</a>
                </li>
                <li class="nav-item<?php if(isset($_GET['page']) && $_GET['page'] == 'school-guidance' || $_GET['page'] == 'teacher' || $_GET['page'] == 'student' || $_GET['page'] == 'parents') echo ' active'; ?>">
                    <a class="nav-link centered" aria-current="page" href="school-guidance">ڕێنمایی قوتابخانە</a>
                </li>
                <li class="nav-item<?php if(isset($_GET['page']) && $_GET['page'] == 'awarenesses') echo ' active'; ?>">
                    <a class="nav-link centered" aria-current="page" href="awarenesses">هۆشیاری</a>
                </li>
                <li class="nav-item<?php if(isset($_GET['page']) && $_GET['page'] == 'online-test') echo ' active'; ?>">
                    <a class="nav-link centered" aria-current="page" href="online-test">تێستی دەروونی</a>
                </li>
                <li class="nav-item<?php if(isset($_GET['page']) && $_GET['page'] == 'counseling' || $_GET['page'] == 'talk-to-me' || $_GET['page'] == 'contact') echo ' active'; ?>">
                    <a class="nav-link centered" aria-current="page" href="counseling">ڕاوێژکاری</a>
                </li>
                <li class="nav-item<?php if(isset($_GET['page']) && $_GET['page'] == 'find-center') echo ' active'; ?>">
                    <a class="nav-link centered" aria-current="page" href="find-center">سەنتەری دەروونی</a>
                </li>
                <li class="nav-item<?php if(isset($_GET['page']) && $_GET['page'] == 'about') echo ' active'; ?>">
                    <a class="nav-link centered" aria-current="page" href="about">دەربارەی ئێمە</a>
                </li>
            </ul>

            <!-- Conditional Login/Dropdown Menu -->
            <div class="centered">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <!-- User Dropdown Menu -->
                    <div class="dropdown w-100 text-center">
                        <button class="btn text-light dropdown-toggle rounded-0 border-0 w-100 nave-item px-4" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="height: 70px !important;">
                            <span>بەخێربێیت &nbsp; <?php echo isset($_SESSION['username']) ? $_SESSION['username'] : 'بەکارهێنەر'; ?> &nbsp; <img class="rounded-5" height="35px" src="<?php echo isset($_SESSION['profile_photo']) ? $_SESSION['profile_photo'] : 'img/unknown.png'; ?>"> &nbsp; </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-start rounded-0 border-0 w-100 text-center" aria-labelledby="userDropdown" style="background-color: #265e6d !important; margin: 0 !important;">
                            <li>
                                <div class="d-flex justify-content-around align-items-center">
                                    <span class="text-light">باڵانس: </span>
                                    <span class="text-light"><?php echo isset($_SESSION['credits']) ? $_SESSION['credits'] : '0'; ?></span>
                                    <a class="btn mx-2 add" href="#addCredit" id="openAddCreditModal">+</a>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <?php if (isset($_SESSION['role']) && $_SESSION['role'] == 'Admin'): ?>
                                <!-- Admin Panel - Visible only to Admins -->
                                <li><a class="dropdown-item nave-item" href="admin">پەناڵی ئەدمین</a></li>
                            <?php endif; ?>
                            <?php if (isset($_SESSION['role']) && in_array($_SESSION['role'], ['Doctor', 'Counsellor', 'Psychologist'])): ?>
                                <!-- Schedule - Visible to doctor, counsellor, and psychologist roles -->
                                <li><a class="dropdown-item nave-item" href="#schedule" id="openScheduleModal">خشتە</a></li>
                            <?php endif; ?>
                            <li><a class="dropdown-item nave-item" href="#profile" id="openProfileModal">پرۆفایل</a></li>
                            <li><a class="dropdown-item nave-item" href="#transactions" id="openTransactionsModal">مامەڵەکان</a></li>
                            <li><a class="dropdown-item nave-item" href="#request" id="openRequestModal">زیادکردنی بابەتی نوێ</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item nave-item hover-red" href="logout.php">چوونەدەر</a></li>
                        </ul>
                    </div>
                <?php else: ?>
                    <!-- Login Button -->
                    <button class="btn text-light outline-none rounded-0 border-0 w-100 text-center nave-item px-5" data-bs-toggle="modal" data-bs-target="#loginModal" style="height: 70px !important;">
                        چوونەژوور
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>