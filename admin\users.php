<?php
session_start();
include '../config.php'; // This will include db.php automatically

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

if ($_SESSION['role'] !== 'Admin') {
    header('Location: ../index.php?page=home');
    exit();
}

// Fetch users from the database
$result = $con->query("SELECT id, username, email, role, created_at FROM users ORDER BY created_at DESC");
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>بەڕێوەبردنی بەکارهێنەران - ئیلهامبەخشی دەروونی</title>
    <?php include '../includes/head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(../font/Rabar_22.ttf);
        }
        @font-face {
            font-family: RabarBold;
            src: url(../font/Rabar_21.ttf);
        }
        body {
            font-family: <PERSON><PERSON>, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding-top: 0;
        }
        .admin-container {
            padding: 2rem 0;
            min-height: 100vh;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .table {
            font-family: Rabar;
        }
        .table th {
            background: linear-gradient(135deg, #265e6d, #10af9c);
            color: white;
            border: none;
            font-weight: 600;
        }
        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
        }
        .btn-edit {
            background: #ed7014;
            border: none;
            color: white;
            padding: 0.375rem 0.75rem;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        .btn-edit:hover {
            background: #d35400;
            color: white;
            transform: translateY(-1px);
        }
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .role-admin { background: #dc3545; color: white; }
        .role-doctor { background: #28a745; color: white; }
        .role-psychologist { background: #17a2b8; color: white; }
        .role-counsellor { background: #ffc107; color: #212529; }
        .role-user { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <?php include '../includes/admin_navbar.php'; ?>
    
    <div class="admin-container">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="admin-card">
                        <h2 class="mb-4">بەڕێوەبردنی بەکارهێنەران</h2>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ناو</th>
                                        <th>ئیمەیڵ</th>
                                        <th>ڕۆڵ</th>
                                        <th>بەروار</th>
                                        <th>کردارەکان</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($user = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <span class="role-badge role-<?php echo strtolower($user['role']); ?>">
                                                <?php 
                                                $roles = [
                                                    'Admin' => 'بەڕێوەبەر',
                                                    'Doctor' => 'دکتۆر',
                                                    'Psychologist' => 'دەروونناس',
                                                    'Counsellor' => 'ڕاوێژکار',
                                                    'User' => 'بەکارهێنەر'
                                                ];
                                                echo $roles[$user['role']] ?? $user['role'];
                                                ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y/m/d', strtotime($user['created_at'])); ?></td>
                                        <td>
                                            <a href="edit_user.php?id=<?php echo $user['id']; ?>" class="btn-edit">
                                                <i class="fas fa-edit me-1"></i>
                                                دەستکاری
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../includes/script.php'; ?>
</body>
</html>
