<?php
session_start();
include 'db.php';
include 'config.php';

// Check for "Remember Me" cookie if the user is not logged in
if (!isset($_SESSION['user_id']) && isset($_COOKIE['remember_me'])) {
    $rememberToken = $_COOKIE['remember_me'];

    // Verify the token in the database
    $query = "SELECT * FROM users WHERE remember_me_token = ? AND remember_me_expiry > NOW()";
    $stmt = $con->prepare($query);
    $stmt->bind_param("s", $rememberToken);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if ($user) {
        // Token is valid, set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
    } else {
        // If the token is invalid or expired, clear the cookie
        setcookie("remember_me", "", time() - 3600, "/");
    }
}

// Check if the user is logged in
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];

    // Retrieve user data from the database
    $stmt = $con->prepare("SELECT profile_photo FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    // Set profile photo path, or default to 'default-profile.png' if not set
    $profilePhotoPath = !empty($user['profile_photo']) ? $user['profile_photo'] : 'default-profile.png';
    $_SESSION['profile_photo'] = $profilePhotoPath;
}
?>
<!doctype html>
<html dir="rtl" lang="ckb">
<?php include "head.php"; ?>
<body>
<?php include "navbar.php"; ?>
<?php
if (isset($_GET['page']) && !empty($_GET['page'])) {
    $page = "pages/" . $_GET['page'] . ".php";
    if (is_file($page)) {
        include $page;
    } else {
        echo "<div class='alert alert-danger'>پەیج بەردەست نییە</div>";
    }
} else {
    // Default to home page
    include "pages/home.php";
}
?>
<?php include "modal.php"; ?>
<?php include "awareness_modal.php";?>
<?php include "script.php"; ?>
</body>
</html>
