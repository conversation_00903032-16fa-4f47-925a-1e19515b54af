<!-- Modal -->
<div class="modal fade" id="requestModal" tabindex="-1" aria-labelledby="requestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="requestModalLabel">شێوازێك هەڵبژێرە</h5>
                <button type="button" class="btn-close close" data-bs-dismiss="modal" aria-label="داخستن"></button>
            </div>
            <div class="modal-body">
                <p class="inLess">بە چ ڕێگەیەك بابەتی نوێ زیاد دەکەیت؟</p>
                <div class="d-grid gap-2 d-flex my-2">
                    <button class="btn btn-primary" onclick="openEmailModal()"><i class="bi bi-envelope-at"></i>
                        ئیمەیڵ</button>
                    <button class="btn btn-success" onclick="sendWhatsApp()"><i class="bi bi-whatsapp"></i>
                        واتسئەپ</button>
                    <button class="btn btn-info" onclick="sendTelegram()"><i class="bi bi-telegram"></i>
                        تێلێگرام</button>
                </div>
                <p class="text-danger p-1 inLess">تێبینی: ئەم کارە پێویستی بە پەسەندکردنە لەلایەن بەرپرسی ماڵپەڕەکە.</p>
            </div>
        </div>
    </div>
</div>

<!-- Email Modal -->
<div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">ناردنی داواکاری بە ئیمەیڵ</h5>
                <button type="button" class="btn-close close" data-bs-dismiss="modal" aria-label="داخستن"></button>
            </div>
            <div class="modal-body">
                <form id="emailForm" action="send_email.php" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="section" class="form-label">بەش</label>
                        <select class="form-select" id="section" name="section">
                            <option value="school_guide_student">ڕێنمایی قوتابخانە / قوتابی</option>
                            <option value="school_guide_teacher">ڕێنمایی قوتابخانە / مامۆستا</option>
                            <option value="awareness">هۆشیاری</option>
                            <option value="counseling_contact">ڕاوێژکاری / پەیوەندی</option>
                            <option value="centers">سەنتەری دەروونی</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">ئیمەیلەکەت</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="subject" class="form-label">سەردێڕ</label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">پەیام</label>
                        <textarea style="height: 150px; resize: none;" class="form-control" id="message" name="message"
                            rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="attachment" class="form-label ms-3">هاوپێچ</label>
                        <label for="attachment" class="custom-file-upload">
                            <span id="file-chosen" class="btn btn-secondary">هاوپێچ هەڵبژێرە</span>
                            <input type="file" class="form-control" id="attachment" name="attachment">
                        </label>
                    </div>
                    <center>
                        <button type="button" class="btn btn-primary px-5 button-teal"
                            onclick="sendEmailRequest()">ناردن</button>
                    </center>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Login Modal -->
<div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loginModalLabel">چوونەژوورەوە</h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="loginEmail" class="form-label">ئیمەیڵ</label>
                        <input type="email" class="form-control" id="loginEmail" name="email"
                            placeholder="ئیمەیڵی ئەلکترۆنی" required>
                    </div>
                    <div class="mb-3">
                        <label for="loginPassword" class="form-label">پاسۆرد</label>
                        <input type="password" class="form-control" id="loginPassword" name="password"
                            placeholder="وشەی نهێنی" required>
                    </div>
                    <div class="form-check mb-3 d-flex align-items-center" style="gap: 8px;">
                        <input type="checkbox" class="form-check-input me-0 ms-auto" id="rememberMe" name="rememberMe">
                        <label class="form-check-label text-end w-100" for="rememberMe">بەبیرم بێنەوە</label>
                    </div>
                    <!-- Forgot Password Link -->
                    <div class="mb-3 w-100">
                        <button type="submit" class="btn btn-primary button-teal">چوونەژوور</button>
                        <a href="#" id="openForgotPasswordModal"
                            class="text-decoration-none float-start align-baseline">پاسۆردت لەیاد کردووە؟</a>
                    </div>
                </form>
                <hr>
                <p class="text-center">ئەکاونتت نییە؟
                    <a href="#" id="openRegisterModal" class="text-decoration-none">لێرە خۆت تۆماربکە</a>
                </p>
            </div>
        </div>
    </div>
</div>


<!-- Forgot Password Modal -->
<div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="forgotPasswordModalLabel">گەڕاندنەوەی پاسۆرد</h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="forgotPasswordForm" action="forgot_password.php" method="post">
                    <div class="mb-3">
                        <label for="forgotEmail" class="form-label">ئیمەیلەکەت</label>
                        <input type="email" class="form-control" id="forgotEmail" name="email"
                            placeholder="ئیمەیڵی ئەلکترۆنی" required>
                    </div>
                    <button type="submit" class="btn btn-primary button-teal">پەسەندکردن</button>
                </form>
            </div>
        </div>
    </div>
</div>


<!-- Registration Modal -->
<div class="modal fade" id="registerModal" tabindex="-1" aria-labelledby="registerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="registerModalLabel">تۆماربوون</h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="registerForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">ناو</label>
                        <input type="text" class="form-control" id="username" name="username"
                            placeholder="ناو بە کوردی بێت باشترە" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">ئیمەیڵ</label>
                        <input type="email" class="form-control" id="registerEmail" name="email"
                            placeholder="ئیمەیڵی ئەلکترۆنی" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">پاسۆرد</label>
                        <input type="password" class="form-control" id="registerPassword" name="password"
                            placeholder="وشەی نهێنی" required>
                    </div>
                    <button type="submit" class="btn btn-primary button-teal">تۆمارکردن</button>
                </form>
                <hr>
                <p class="text-center">
                    <a href="#" id="openTermModal" class="text-decoration-none">مەرج و ڕێساکان</a>
                    <span> و </span>
                    <a href="#" id="openPrivacyModal" class="text-decoration-none">تایبەتمەندی و سیاسەتەکان</a>
                </p>
            </div>
        </div>
    </div>
</div>


<!-- Profile Modal -->
<div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileModalLabel">پڕۆفایل</h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Profile Form -->
                <form id="profileForm" enctype="multipart/form-data">
                    <div class="mb-3 text-center">
                        <div class="profile-image-preview">
                            <img src="<?php echo isset($_SESSION['profile_photo']) ? $_SESSION['profile_photo'] : 'img/unknown.png'; ?>"
                                id="profileImage" alt="Profile Image">
                        </div>
                        <input type="file" class="form-control mt-2" id="profilePhoto" name="profilePhoto"
                            accept="image/*" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="profileUsername" class="form-label">ناوی بەکارهێنەر</label>
                        <input type="text" class="form-control" id="profileUsername" name="username"
                            value="<?php echo isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : ''; ?>"
                            required disabled>
                    </div>
                    <div class="mb-3">
                        <label for="profileEmail" class="form-label">ئیمەیڵ</label>
                        <input type="email" class="form-control" id="profileEmail" name="email"
                            value="<?php echo isset($_SESSION['email']) ? htmlspecialchars($_SESSION['email']) : ''; ?>"
                            required disabled>
                    </div>
                    <div class="mb-3">
                        <label for="profilePassword" class="form-label">وشەی نهێنی نوێ (ئارەزوومەندانە)</label>
                        <input type="password" class="form-control" id="profilePassword" name="password" disabled>
                        <small class="form-text text-muted">وشەی نهێنی نوێکە بنووسە ئەگەر ئەیگۆڕین.</small>
                    </div>
                    <!-- Edit and Update Buttons -->
                    <div class="d-flex justify-content-between">
                        <button type="submit" id="updateButton" class="btn btn-primary button-teal"
                            disabled>پاشەکەوتکردن</button>
                        <button type="button" id="editButton" class="btn btn-secondary">بیگۆرە</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<!-- Schedule Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleModalLabel">خشتەی هەفتانەی</h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <!-- Modal Body -->
            <div class="modal-body">
                <!-- Schedule Form -->
                <form id="scheduleForm">
                    <!-- Day Selection -->
                    <div class="mb-3">
                        <label for="dayOfWeek" class="form-label">ڕۆژێك دیاری بکە</label>
                        <select class="form-select" id="dayOfWeek" name="day_of_week" required>
                            <option value="Saturday">شەممە</option>
                            <option value="Sunday">یەك شەممە</option>
                            <option value="Monday">دوو شەممە</option>
                            <option value="Tuesday">سێ شەممە</option>
                            <option value="Wednesday">چوار شەممە</option>
                            <option value="Thursday">پێنج شەممە</option>
                            <option value="Friday">هەینی</option>
                        </select>
                    </div>
                    <!-- Time Slot Container -->
                    <div id="timeSlotContainer">
                        <!-- Time Slot Row Template -->
                        <div class="row time-slot mb-3 align-items-end">
                            <div class="col-4">
                                <label class="form-label">دەستپێکردن</label>
                                <input type="time" class="form-control" name="start_time[]" required>
                            </div>
                            <div class="col-4">
                                <label class="form-label">کۆتاییپێهێنان</label>
                                <input type="time" class="form-control" name="end_time[]" required>
                            </div>
                            <div class="col-2">
                                <button type="button" class="btn btn-danger remove-time-slot mt-2">لابردن</button>
                            </div>
                        </div>
                    </div>
                    <!-- Button to Add More Time Slots -->
                    <button type="button" class="btn btn-secondary mb-3" id="addTimeSlot">زیادکردنی کات تر</button>
                    <!-- Submit Button -->
                    <!-- View and Update Buttons -->
                    <div class="d-flex justify-content-between mt-3">
                        <button type="button" class="btn btn-info" data-bs-toggle="modal"
                            data-bs-target="#weekScheduleModal" id="viewWeekSchedule">بینینی خشتەکە</button>
                        <button type="submit" class="btn btn-primary button-teal">پاشەکەوتکردن</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<!-- Week Schedule Modal -->
<div class="modal fade" id="weekScheduleModal" tabindex="-1" aria-labelledby="weekScheduleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="weekScheduleModalLabel">خشتەی هەفتانەی</h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="weekScheduleContainer" class="table-responsive">
                    <!-- Weekly schedule content will be loaded here -->
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>ڕۆژ</th>
                                <th>کاتەکان</th>
                            </tr>
                        </thead>
                        <tbody id="weekScheduleBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Add Credit Modal -->
<div class="modal fade" id="addCreditModal" tabindex="-1" aria-labelledby="addCreditModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCreditModalLabel">زیادکردنی باڵانس</h5>
                <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="credit-options">
                    <!-- Option 1 -->
                    <label class="credit-card">
                        <input type="radio" name="credit_option" value="1">
                        <div class="credit-card-content">
                            <h3>1<br>باڵانس</h3>
                            <h2>1,750 IQD</h2>
                            <ul>
                                <li>باڵانس بۆ یەك جار<br>بەکارهێنان.</li>
                            </ul>
                        </div>
                    </label>

                    <!-- Option 2 (Recommended) -->
                    <label class="credit-card recommended">
                        <input type="radio" name="credit_option" value="3" checked>
                        <div class="credit-card-content">
                            <h3>3<br>باڵانس</h3>
                            <h2>4,750 IQD</h2>
                            <ul>
                                <li>باڵانس بۆ سێ جار<br>بەکارهێنان.</li>
                            </ul>
                        </div>
                    </label>

                    <!-- Option 3 -->
                    <label class="credit-card">
                        <input type="radio" name="credit_option" value="5">
                        <div class="credit-card-content">
                            <h3>5<br>باڵانس</h3>
                            <h2>7,750 IQD</h2>
                            <ul>
                                <li>باڵانس بۆ پێنج جار<br>بەکارهێنان.</li>
                            </ul>
                        </div>
                    </label>
                </div>

                <!-- Payment Method Section -->
                <div class="payment-methods mt-4">
                    <h6>شێوازی پارەدان هەڵبژێرە:</h6>
                    <label class="payment-method">
                        <input type="radio" name="payment_method" value="FastPay" checked>
                        <img class="bg-white rounded-2 p-2" src="img/fastpay.png" alt="FastPay Logo">
                    </label>

                    <label class="payment-method">
                        <input type="radio" name="payment_method" value="FIB">
                        <img class="bg-white rounded-2 p-2" src="img/fib.png" alt="FIB Logo">
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn text-light button-teal">زیادکردن</button>
            </div>
        </div>
    </div>
</div>